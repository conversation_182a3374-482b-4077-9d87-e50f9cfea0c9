import { useSidebar } from '@rabbit/design-system/components/app-sidebar/sidebar-context';
import { cn } from '@rabbit/design-system/lib/utils';
import React from 'react';
import type {
  SidebarDropdownItem,
  SidebarNavItem,
} from './data/sidebar.config';
import SidebarDropdown from './sidebar-dropdown-button';

interface SidebarNavProps {
  items: SidebarNavItem[];
}

export default function SidebarNav({ items }: SidebarNavProps) {
  const { isOpen } = useSidebar();

  // Render a regular button (non-dropdown)
  const renderNavButton = (item: SidebarNavItem) => {
    return (
      <a
        key={`${item.title}-${item.href}`}
        href={item.href || '#'}
        style={{
          backgroundColor: '#1c1c1c',
          borderColor: '#2c2c2c',
        }}
        className={cn(
          'sidebar-button flex h-10 items-center px-2.5',
          'hover:sidebar-button-hover transition-colors',
          'rounded-[2px] border'
        )}
      >
        <div
          className={cn(
            'flex items-center justify-center',
            isOpen ? 'mr-3 w-6' : 'w-full'
          )}
        >
          {React.createElement(item.icon, { className: 'h-5 w-5' })}
        </div>

        {isOpen && (
          <span className="font-sidebar text-sidebar-foreground text-sm tracking-wide">
            {item.title}
          </span>
        )}
      </a>
    );
  };

  return (
    <div className="space-y-1 px-2">
      {items.map((item) => (
        <div key={`nav-${item.title}-${item.href}`} className="py-1">
          {item.children?.length ? (
            <SidebarDropdown item={item as SidebarDropdownItem} />
          ) : (
            renderNavButton(item)
          )}
        </div>
      ))}
    </div>
  );
}
