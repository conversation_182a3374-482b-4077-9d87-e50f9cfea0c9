export default function FontTest() {
  return (
    <div className="space-y-4 p-4">
      <h2 className="mb-4 text-xl">Font Style Test</h2>

      <div className="rounded bg-[#121212] p-4">
        <h3 className="mb-2 text-white">Sidebar Font (Capitalize)</h3>
        <span className="font-sidebar text-white">Home Events Messages</span>

        <h3 className="mt-4 mb-2 text-white">Sidebar Font (Uppercase)</h3>
        <span className="font-sidebar text-white uppercase">
          Home Events Messages
        </span>

        <h3 className="mt-4 mb-2 text-white">Sidebar Font (Normal)</h3>
        <span className="font-sidebar text-white normal-case">
          home events messages
        </span>
      </div>

      <div className="mt-4 rounded bg-[#121212] p-4">
        <h3 className="mb-2 text-white">Regular Rubik (font-rubik)</h3>
        <span className="font-rubik text-sm text-white tracking-wide">
          This text uses the regular font-rubik class
        </span>
      </div>

      <div className="mt-4 rounded bg-[#121212] p-4">
        <h3 className="mb-2 text-white">Rubik Font Weight Comparison</h3>
        <div className="space-y-2">
          <p className="font-light font-rubik text-sm text-white tracking-wide">
            Rubik Light (300) - The weight used for sidebar
          </p>
          <p className="font-normal font-rubik text-sm text-white tracking-wide">
            Rubik Normal (400)
          </p>
          <p className="font-medium font-rubik text-sm text-white tracking-wide">
            Rubik Medium (500)
          </p>
          <p className="font-rubik font-semibold text-sm text-white tracking-wide">
            Rubik Semibold (600)
          </p>
          <p className="font-bold font-rubik text-sm text-white tracking-wide">
            Rubik Bold (700)
          </p>
        </div>
      </div>
    </div>
  );
}
