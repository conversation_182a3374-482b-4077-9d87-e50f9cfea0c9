'use client';

import { useSidebar } from '@rabbit/design-system/components/app-sidebar/sidebar-context';
import { cn } from '@rabbit/design-system/lib/utils';
import type { SidebarButtonItem } from '@rabbit/utils/types/sidebar';
import SidebarBaseButton from './sidebar-base-button';

interface SidebarButtonGroupProps {
  items: SidebarButtonItem[];
  title?: string;
}

export default function SidebarButtonGroup({
  items,
  title,
}: SidebarButtonGroupProps) {
  const { isOpen } = useSidebar();

  return (
    <div className="px-2 py-2">
      {title && isOpen && (
        <div className="mb-1 px-2 font-medium text-sidebar-text-muted text-xs uppercase">
          {title}
        </div>
      )}
      <div className="flex flex-col">
        {items.map((item, index) => {
          const isFirst = index === 0;
          const isLast = index === items.length - 1;

          return (
            <div
              key={`group-${item.title}-${index}`}
              className={cn(
                // Border styling
                isFirst && 'rounded-t-[2px]',
                isLast && 'rounded-b-[2px]',
                // Overlap borders between buttons
                index > 0 && '-mt-[1px]'
              )}
            >
              <SidebarBaseButton
                title={item.title}
                icon={item.icon}
                href={item.href}
                hasBorder={true}
                isActive={item.isActive}
                disabled={item.disabled}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
