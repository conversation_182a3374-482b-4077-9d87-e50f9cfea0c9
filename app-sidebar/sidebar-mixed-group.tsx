import { useSidebar } from '@rabbit/design-system/components/app-sidebar/sidebar-context';
import { sidebarClasses } from '@rabbit/design-system/data/sidebar-classes';
import { cn } from '@rabbit/design-system/lib/utils';
import type {
  SidebarButtonItem,
  SidebarMixedGroupConfig,
} from '@rabbit/utils/types/sidebar';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';
import SidebarBaseButton from './sidebar-base-button';

interface SidebarMixedGroupProps {
  config: SidebarMixedGroupConfig;
}

export default function SidebarMixedGroup({ config }: SidebarMixedGroupProps) {
  const { isOpen } = useSidebar();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const toggleDropdown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="px-2 py-2">
      <div className="flex flex-col">
        {/* Solo buttons section */}
        {config.soloButtons.map((button: SidebarButtonItem, index: number) => {
          const isFirst = index === 0;

          return (
            <div
              key={`solo-${button.title}`}
              className={cn(
                isFirst && 'rounded-t-[2px]',
                // Overlap borders between buttons to avoid double-borders
                index > 0 && '-mt-[1px]'
              )}
              style={{
                borderBottomLeftRadius: 0,
                borderBottomRightRadius: 0,
              }}
            >
              <SidebarBaseButton
                title={button.title}
                icon={button.icon}
                href={button.href}
                hasBorder={true}
              />
            </div>
          );
        })}

        {/* Dropdown button */}
        <div className={config.soloButtons.length > 0 ? '-mt-[1px]' : ''}>
          <button
            type="button"
            onClick={toggleDropdown}
            className={cn(
              'group flex h-10 items-center text-sidebar-foreground transition-colors',
              'pl-[10px]',
              'border border-sidebar-border',
              !isDropdownOpen && 'rounded-b-[2px]',
              sidebarClasses.animations.container,
              isOpen ? 'w-full' : 'w-[40px]'
            )}
            style={{
              backgroundColor: isDropdownOpen
                ? 'var(--sidebar-dropdown-bg)'
                : 'var(--sidebar-button-bg)',
              borderColor: 'var(--sidebar-border)',
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0,
            }}
          >
            <div className="flex h-5 w-5 min-w-5 items-center justify-center">
              {config.dropdownButton.icon &&
                React.createElement(config.dropdownButton.icon, {
                  className: 'h-5 w-5',
                })}
            </div>

            <AnimatePresence mode="wait">
              {isOpen && (
                <motion.span
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: 'auto' }}
                  exit={{ opacity: 0, width: 0 }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                  className={cn(
                    'ml-[10px] font-medium text-sm tracking-wide',
                    sidebarClasses.overflowControl
                  )}
                >
                  {config.dropdownButton.title}
                </motion.span>
              )}
            </AnimatePresence>

            {/* Only show chevron when dropdown is open */}
            <AnimatePresence>
              {isDropdownOpen && isOpen && (
                <motion.div
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: 'auto' }}
                  exit={{ opacity: 0, width: 0 }}
                  className="ml-auto pr-[10px]"
                >
                  {isDropdownOpen ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </button>

          {/* Dropdown Content */}
          <AnimatePresence>
            {isDropdownOpen && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="overflow-hidden"
              >
                <div
                  className={cn(
                    'rounded-b-[2px] border-sidebar-border border-x border-b',
                    sidebarClasses.animations.container,
                    isOpen ? 'w-full' : 'w-[40px]'
                  )}
                  style={{
                    backgroundColor: 'var(--sidebar-bg)',
                    boxShadow:
                      'inset 0 1px 3px rgba(0, 0, 0, 0.2), inset 0 -1px 3px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  {config.dropdownButton.items.map(
                    (item: SidebarButtonItem, itemIndex: number) => (
                      <a
                        key={`dropdown-${item.title}-${itemIndex}`}
                        href={item.href}
                        className={cn(
                          'group flex h-10 w-full items-center text-sidebar-foreground transition-colors hover:bg-sidebar-muted',
                          'pl-[10px]'
                        )}
                      >
                        <div className="flex h-5 w-5 min-w-5 items-center justify-center">
                          {React.createElement(item.icon, {
                            className: 'h-5 w-5',
                          })}
                        </div>

                        <AnimatePresence mode="wait">
                          {isOpen && (
                            <motion.span
                              initial={{ opacity: 0, width: 0 }}
                              animate={{ opacity: 1, width: 'auto' }}
                              exit={{ opacity: 0, width: 0 }}
                              transition={{ duration: 0.3, ease: 'easeInOut' }}
                              className={cn(
                                'ml-[10px] font-medium text-sm uppercase tracking-wide',
                                sidebarClasses.overflowControl
                              )}
                            >
                              {item.title}
                            </motion.span>
                          )}
                        </AnimatePresence>
                      </a>
                    )
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
