"use client";
import { Icons } from "@midday/ui/icons";
import Link from "next/link";
import { useSidebar } from "./sidebar-context";

/**
 * Sidebar header component that displays the logo
 *
 * @returns SidebarHeader component
 */
export default function SidebarHeader() {
  const { isOpen } = useSidebar();

  return (
    <Link href="/" className="flex items-center justify-center h-full">
      {isOpen ? (
        <Icons.Logo className="h-6" />
      ) : (
        <Icons.LogoSmall className="h-6" />
      )}
    </Link>
  );
}
