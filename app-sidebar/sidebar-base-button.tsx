import { useSidebar } from '@rabbit/design-system/components/app-sidebar/sidebar-context';
import { sidebarClasses } from '@rabbit/design-system/data/sidebar-classes';
import { cn } from '@rabbit/design-system/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import React from 'react';

export type SidebarBaseButtonProps = {
  // Core properties
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;

  // Visual customization
  isActive?: boolean;
  isDropdownOpen?: boolean;
  showChevron?: boolean;
  hasBorder?: boolean;
  disabled?: boolean;

  // Event handlers
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
};

export default function SidebarBaseButton({
  title,
  icon,
  href = '#',
  isActive = false,
  isDropdownOpen = false,
  showChevron = false,
  hasBorder = true,
  disabled = false,
  onClick,
}: SidebarBaseButtonProps) {
  const { isOpen } = useSidebar();

  // Determine if this button is a clickable link or just a toggle button
  const isInteractive = !disabled && !!href && href !== '#';

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (disabled) {
      e.preventDefault();
      return;
    }
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <a
      href={isInteractive ? href : undefined}
      onClick={handleClick}
      className={cn(
        // Base button styling
        'group flex h-10 items-center transition-colors',
        'text-sidebar-foreground',

        // Consistent padding regardless of sidebar state
        'pl-[10px]',

        // Border styling
        hasBorder && 'border border-sidebar-border',

        // Width for collapsed state with smooth animation
        sidebarClasses.animations.container,
        isOpen ? 'w-full' : 'w-[40px]',

        // Hover state
        !disabled && 'hover:bg-sidebar-muted',

        // Disabled state
        disabled && 'cursor-not-allowed opacity-50'
      )}
      style={{
        // Background color based on state
        backgroundColor: isDropdownOpen
          ? 'var(--sidebar-dropdown-bg)'
          : 'var(--sidebar-button-bg)',
        // Border color
        borderColor: 'var(--sidebar-border)',
      }}
      aria-disabled={disabled}
    >
      {/* Icon container with fixed sizing and position */}
      <div className="flex h-5 w-5 min-w-5 items-center justify-center">
        {icon &&
          React.createElement(icon, {
            className: cn(
              'h-5 w-5',
              isActive && !disabled && 'text-sidebar-accent',
              disabled && 'text-gray-500'
            ),
          })}
      </div>

      {/* Animated title container */}
      <AnimatePresence mode="wait">
        {isOpen && (
          <motion.span
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: 'auto' }}
            exit={{ opacity: 0, width: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className={cn(
              'ml-[10px] font-sidebar',
              sidebarClasses.overflowControl,
              isActive && !disabled && 'text-sidebar-accent',
              disabled && 'text-gray-500'
            )}
          >
            {title}
          </motion.span>
        )}
      </AnimatePresence>

      {/* Chevron only displayed when requested and sidebar is open */}
      {showChevron && (
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: 'auto' }}
              exit={{ opacity: 0, width: 0 }}
              className="ml-auto pr-[10px]"
            >
              {/* Chevron icon would go here */}
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </a>
  );
}
