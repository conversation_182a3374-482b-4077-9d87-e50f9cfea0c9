import { cn } from "@rabbit/design-system/lib/utils";
import type { ReactNode } from "react";

interface SidebarContainerProps {
  isOpen: boolean;
  children: ReactNode;
}

/**
 * Main sidebar container that adapts to open/closed states
 *
 * @param props The component props
 * @param props.isOpen Whether the sidebar is expanded or collapsed
 * @param props.children Content to render inside the sidebar
 * @returns Sidebar container component
 */
export function SidebarContainer({ isOpen, children }: SidebarContainerProps) {
  return (
    <aside
      className={cn(
        "dashboard-sidebar",
        isOpen && "w-60",
        !isOpen && "w-[var(--sidebar-width)]",
      )}
    >
      {children}
    </aside>
  );
}
