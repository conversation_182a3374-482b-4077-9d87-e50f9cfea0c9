"use client";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { SidebarContainer } from "./sidebar-container";
import { SidebarContent } from "./sidebar-content";
import { useSidebar } from "./sidebar-context";
import SidebarFooter from "./sidebar-footer";
import SidebarHeader from "./sidebar-header";

/**
 * Main sidebar component that includes header, content, footer and toggle button
 *
 * @returns AppSidebar component
 */
export function AppSidebar() {
  const { isOpen, toggleSidebar, isTransitioning } = useSidebar();

  return (
    <SidebarContainer isOpen={isOpen}>
      {/* Header with logo */}
      <div className="dashboard-sidebar-logo-container">
        <SidebarHeader />
      </div>

      {/* Main navigation content */}
      <div className="flex flex-col items-center justify-center w-full mt-6">
        <SidebarContent />
      </div>

      {/* Footer section */}
      <SidebarFooter />

      {/* Toggle button - don't show during transitions for smoother animations */}
      {!isTransitioning && (
        <button
          type="button"
          onClick={toggleSidebar}
          className="absolute top-1/2 right-[-12px] -translate-y-1/2 z-30 size-6 flex items-center justify-center rounded-full border border-sidebar-item-border-active bg-background"
          aria-label={isOpen ? "Collapse sidebar" : "Expand sidebar"}
        >
          {isOpen ? (
            <ChevronLeft className="size-4" />
          ) : (
            <ChevronRight className="size-4" />
          )}
        </button>
      )}
    </SidebarContainer>
  );
}

export default AppSidebar;
