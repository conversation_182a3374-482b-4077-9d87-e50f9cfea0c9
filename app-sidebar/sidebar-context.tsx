'use client';

import { useSidebar as useAtomSidebar } from '@rabbit/design-system/atoms/sidebar';
import type React from 'react';
import { createContext, useContext, useEffect, useState } from 'react';

type SidebarContextType = {
  isOpen: boolean;
  isTransitioning: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  openSidebar: () => void;
  isMobile: boolean;
  isMessageSidebarOpen: boolean;
  toggleMessageSidebar: () => void;
};

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const { isOpen, isTransitioning, toggleSidebar, closeSidebar, openSidebar } =
    useAtomSidebar();

  const [isMobile, setIsMobile] = useState(false);
  const [isMessageSidebarOpen, setIsMessageSidebarOpen] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        closeSidebar();
      }
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);

    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, [closeSidebar]);

  const toggleMessageSidebar = () => {
    setIsMessageSidebarOpen(!isMessageSidebarOpen);
  };

  return (
    <SidebarContext.Provider
      value={{
        isOpen,
        isTransitioning,
        toggleSidebar,
        closeSidebar,
        openSidebar,
        isMobile,
        isMessageSidebarOpen,
        toggleMessageSidebar,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}
