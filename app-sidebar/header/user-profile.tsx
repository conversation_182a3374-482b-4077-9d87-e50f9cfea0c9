'use client';
import { useSidebarTransition } from '@rabbit/design-system/atoms/sidebar-atom';
import { sidebarClasses } from '@rabbit/design-system/data/sidebar-classes';
import { cn } from '@rabbit/design-system/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { mockUser } from './mock-user';
import UserProfileBadge from './user-profile-badge';
import UserProfileName from './user-profile-name';
import UserProfileProgress from './user-profile-progress';

interface UserProfileProps {
  isOpen: boolean;
}

export default function UserProfile({ isOpen }: UserProfileProps) {
  const { name, level, progress } = mockUser;
  const { isTransitioning } = useSidebarTransition();

  return (
    <div className={cn(sidebarClasses.profile.container, 'py-2')}>
      <div className="w-full">
        <AnimatePresence mode="wait">
          {isOpen ? (
            <motion.div
              key="expanded"
              className={sidebarClasses.profile.expandedAlignment}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{
                duration: 0.2,
                ease: [0.16, 1, 0.3, 1],
              }}
            >
              <UserProfileName name={name} />
              <UserProfileBadge level={level} />
            </motion.div>
          ) : (
            <motion.div
              key="collapsed"
              className={sidebarClasses.profile.collapsedAlignment}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{
                duration: 0.2,
                ease: [0.16, 1, 0.3, 1],
              }}
            >
              <UserProfileBadge level={level} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <motion.div
        layout
        className={cn(sidebarClasses.profile.progress)}
        transition={{
          duration: 0.3,
          ease: [0.16, 1, 0.3, 1],
        }}
      >
        <UserProfileProgress progress={progress} />
      </motion.div>
    </div>
  );
}
