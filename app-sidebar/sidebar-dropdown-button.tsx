import { useSidebar } from '@rabbit/design-system/components/app-sidebar/sidebar-context';
import { sidebarClasses } from '@rabbit/design-system/data/sidebar-classes';
import { cn } from '@rabbit/design-system/lib/utils';
import type { SidebarButtonItem } from '@rabbit/utils/types/sidebar';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState } from 'react';

interface SidebarDropdownItem {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  dropdown?: {
    items: SidebarButtonItem[];
  };
}

interface SidebarDropdownButtonProps {
  item: SidebarDropdownItem;
}

export default function SidebarDropdownButton({
  item,
}: SidebarDropdownButtonProps) {
  const { isOpen } = useSidebar();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const hasDropdown = !!item.dropdown?.items.length;

  return (
    <div className={sidebarClasses.dropdownContainer}>
      <div>
        <a
          href={hasDropdown ? undefined : item.href || '#'}
          onClick={(e) => {
            if (hasDropdown) {
              e.preventDefault();
              toggleDropdown();
            }
          }}
          style={{
            backgroundColor: isDropdownOpen
              ? 'var(--sidebar-dropdown-bg)'
              : 'var(--sidebar-button-bg)',
            borderColor: 'var(--sidebar-border)',
          }}
          className={cn(
            'group flex h-10 items-center',
            'transition-colors hover:bg-sidebar-muted',
            'pl-[10px]',
            'rounded-t-[2px]',
            !isDropdownOpen && 'rounded-b-[2px]',
            'border border-sidebar-border',
            sidebarClasses.animations.container,
            isOpen ? 'w-full' : 'w-[40px]'
          )}
        >
          <div className="flex h-5 w-5 min-w-5 items-center justify-center">
            {item.icon &&
              React.createElement(item.icon, { className: 'h-5 w-5' })}
          </div>

          <AnimatePresence mode="wait">
            {isOpen && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className={cn(
                  'ml-[10px] font-sidebar',
                  sidebarClasses.overflowControl
                )}
              >
                {item.title}
              </motion.span>
            )}
          </AnimatePresence>

          {hasDropdown && (
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: 'auto' }}
                  exit={{ opacity: 0, width: 0 }}
                  className="ml-auto pr-[10px]"
                >
                  {isDropdownOpen ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          )}
        </a>

        {/* Dropdown Content */}
        <AnimatePresence>
          {hasDropdown && isDropdownOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="overflow-hidden"
            >
              <div
                className={cn(
                  'box-border rounded-b-[2px] border-sidebar-border border-x border-b',
                  sidebarClasses.animations.container,
                  isOpen ? 'w-full' : 'w-[40px]'
                )}
                style={{
                  backgroundColor: 'var(--sidebar-bg)',
                  boxShadow:
                    'inset 0 1px 3px rgba(0, 0, 0, 0.2), inset 0 -1px 3px rgba(0, 0, 0, 0.1), 1px 0 0 var(--sidebar-border)',
                }}
              >
                {item.dropdown?.items.map(
                  (dropdownItem: SidebarButtonItem, dropIndex: number) => (
                    <a
                      key={`dropdown-${dropdownItem.title}-${dropIndex}`}
                      href={dropdownItem.href}
                      className={cn(
                        'group flex h-10 w-full items-center text-sidebar-foreground transition-colors hover:bg-sidebar-muted',
                        'pl-[10px]'
                      )}
                    >
                      <div className="flex h-5 w-5 min-w-5 items-center justify-center">
                        {React.createElement(dropdownItem.icon, {
                          className: 'h-5 w-5',
                        })}
                      </div>

                      <AnimatePresence mode="wait">
                        {isOpen && (
                          <motion.span
                            initial={{ opacity: 0, width: 0 }}
                            animate={{ opacity: 1, width: 'auto' }}
                            exit={{ opacity: 0, width: 0 }}
                            transition={{ duration: 0.3, ease: 'easeInOut' }}
                            className={cn(
                              'ml-[10px] font-sidebar',
                              sidebarClasses.overflowControl
                            )}
                          >
                            {dropdownItem.title}
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </a>
                  )
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
