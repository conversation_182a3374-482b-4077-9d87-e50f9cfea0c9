'use client';

import {
  getAdminSidebarNav,
  getAuthSidebarNav,
  getPublicSidebarNav,
} from '@rabbit/design-system/config/navigations/sidebar.config';
import useUser from '@rabbit/design-system/hooks/use-user';
import type { LucideIcon } from 'lucide-react';
import { User } from 'lucide-react';
import { useMemo } from 'react';

import type { SidebarNavigation } from '@rabbit/utils/types/sidebar';
import {
  isSidebarDropdownGroup,
  isSidebarNavItem,
} from '@rabbit/utils/types/sidebar';
import SidebarBaseButton from './sidebar-base-button';
import SidebarButtonGroup from './sidebar-button-group';

export function SidebarContent() {
  const user = useUser();
  const isAuthenticated = !!user;

  const sidebarNav = useMemo<SidebarNavigation>(() => {
    try {
      return [
        ...getPublicSidebarNav(),
        ...(isAuthenticated ? getAuthSidebarNav() : []),
        ...(user?.role === 'ADMIN' ? getAdminSidebarNav() : []),
      ];
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: <explanation>
      console.error('Error loading sidebar navigation:', error);
      // Fallback to just public nav if there's an error
      return getPublicSidebarNav();
    }
  }, [isAuthenticated, user?.role]);

  // Helper to ensure icon is a component
  const getIconComponent = (icon: LucideIcon | string) => {
    if (typeof icon === 'string') {
      // Return a default icon if it's a string
      return User;
    }
    return icon;
  };

  return (
    <div className="flex w-full flex-1 flex-col">
      <div className="sidebar-scroll flex-1 overflow-y-auto py-2">
        {sidebarNav.map((entry, index) => {
          if (isSidebarDropdownGroup(entry)) {
            // This is a group
            return (
              <SidebarButtonGroup
                key={`group-${entry.label}-${index}`}
                items={entry.items.map((item) => ({
                  title: item.name,
                  icon: getIconComponent(item.icon),
                  href: item.href,
                  disabled: item.disabled,
                }))}
              />
            );
          }

          if (isSidebarNavItem(entry)) {
            // This is a single item
            return (
              <div className="px-2 py-2" key={`solo-${entry.name}-${index}`}>
                <SidebarBaseButton
                  title={entry.name}
                  icon={getIconComponent(entry.icon)}
                  href={entry.href}
                />
              </div>
            );
          }

          return null; // Invalid entry type
        })}
      </div>
    </div>
  );
}
