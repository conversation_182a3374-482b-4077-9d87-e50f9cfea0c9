/*
 * colors.css
 *
 * Core color generation system using OKLCH color space 
 * for consistent and accessible color palettes.
 */

/* Core Gray Palette */
@layer base {
  :root {
    /* --- Primary Color HSL (for theme picker overlays) --- */
    --primary-hsl: var(--color-red-500); /* Updated: using red theme token */ /* Updated: violet */ /* Default: blue */

    /* Gray Palette (OKLCH for better color accuracy) */
    --color-oklch-white: oklch(100% 0 0); /* #ffffff */
    --color-oklch-gray-50: oklch(98.48% 0 0); /* #fafafa - Code BG Light */
    --color-oklch-gray-100: oklch(96.71% 0.003 265); /* #f5f5f5 - Tab/Button BG Light */
    --color-oklch-gray-100-ish: oklch(97% 0.002 270); /* #f6f6f7 - Alt Tab/Button BG Light */
    --color-oklch-gray-100-active: oklch(94.5% 0.004 270); /* #eff0f3 - Active Tab BG Light */
    --color-oklch-gray-200: oklch(92.79% 0.004 286); /* #e5e7eb - Border Light / Tab Text Dark */
    --color-oklch-gray-200-ish: oklch(91.5% 0.004 270); /* #e0e1e3 - Alt Border Light */
    --color-oklch-gray-300-code: oklch(85.5% 0 0); /* #d1d1d1 - Code Text Dark */
    --color-oklch-gray-400-tab: oklch(81.5% 0.003 270); /* #c4c5c6 - Tab/Button Text Dark */
    --color-oklch-slate-700: oklch(43.91% 0.034 254); /* #334155 - Default Text Light */
    --color-oklch-gray-text-code: oklch(42.75% 0 0); /* #414141 - Code Text Light */
    --color-oklch-gray-text-tab: oklch(50.5% 0.023 264); /* #4b5563 - Tab Text Light */
    --color-oklch-gray-100-dark: oklch(96% 0.003 260); /* #f3f4f6 - Default Text Dark */

    --color-oklch-gray-border-dark: oklch(33.58% 0 0); /* #292929 */
    --color-oklch-gray-border-dark-ish: oklch(31.85% 0 0); /* #262626 */
    --color-oklch-gray-bg-dark-code: oklch(26.61% 0 0); /* #1a1a1a */
    --color-oklch-gray-bg-dark-tab: oklch(22.39% 0 0); /* #141414 */
    --color-oklch-gray-bg-dark-big-code-1: oklch(27.91% 0 0); /* #1c1c1c */
    --color-oklch-gray-bg-dark-big-code-2: oklch(32.8% 0 0); /* #282828 */
    --color-oklch-near-black: oklch(18.29% 0 0); /* #111111 */
    --color-oklch-black: oklch(0% 0 0);

    /* Black Shades for Dark Mode */
    --color-black-base: oklch(3.14% 0 0); /* #080808 - Base black */
    --color-black-50: oklch(4.71% 0 0); /* #0C0C0C */
    --color-black-100: oklch(6.27% 0 0); /* #101010 */
    --color-black-200: oklch(7.84% 0 0); /* #141414 */
    --color-black-300: oklch(9.41% 0 0); /* #181818 */
    --color-black-400: oklch(10.98% 0 0); /* #1C1C1C */
    --color-black-500: oklch(12.55% 0 0); /* #202020 */
    --color-black-600: oklch(14.12% 0 0); /* #242424 */
    --color-black-700: oklch(15.69% 0 0); /* #282828 */
    --color-black-800: oklch(17.25% 0 0); /* #2C2C2C */
    --color-black-900: oklch(18.82% 0 0); /* #303030 */
    --color-black-950: oklch(20.39% 0 0); /* #343434 */

    /* Form specific variables */
    --form-bg: #0c0c0c;
    --form-text: #ffffff;
    --form-text-muted: #878787;
    --form-input-bg: #141414;
    --form-input-border: #2c2c2c;
    --form-input-text: #ffffff;
    --form-label-text: #878787;
    --form-button-height: 40px;
    --form-input-height: 36px;
    --form-input-radius: 0.5rem;
    --form-valid-indicator: #4ade80; /* Green */
    --form-invalid-indicator: #ef4444; /* Red */
    --form-neutral-indicator: #6b7280; /* Gray */
    --form-link-text: var(--sidebar-accent);
    --form-max-width: 380px;

    /* Blue Palette (Primary) */
    --color-blue-50: oklch(97% 0.02 240);
    --color-blue-100: oklch(94% 0.04 240);
    --color-blue-200: oklch(90% 0.08 240);
    --color-blue-300: oklch(86% 0.11 240);
    --color-blue-400: oklch(79% 0.14 240);
    --color-blue-500: oklch(74% 0.17 240);
    --color-blue-600: oklch(68% 0.18 240);
    --color-blue-700: oklch(60% 0.17 240);
    --color-blue-800: oklch(52% 0.15 240);
    --color-blue-900: oklch(46% 0.13 240);
    --color-blue-950: oklch(38% 0.11 240);

    /* Red Palette */
    --color-red-50: oklch(97% 0.02 25);
    --color-red-100: oklch(94% 0.04 25);
    --color-red-200: oklch(90% 0.08 25);
    --color-red-300: oklch(86% 0.12 25);
    --color-red-400: oklch(79% 0.16 25);
    --color-red-500: oklch(73% 0.2 25);
    --color-red-600: oklch(65% 0.22 25);
    --color-red-700: oklch(57% 0.21 25);
    --color-red-800: oklch(49% 0.18 25);
    --color-red-900: oklch(41% 0.15 25);
    --color-red-950: oklch(33% 0.12 25);

    /* Orange Palette */
    --color-orange-50: oklch(97% 0.02 50);
    --color-orange-100: oklch(94% 0.04 50);
    --color-orange-200: oklch(90% 0.07 50);
    --color-orange-300: oklch(86% 0.11 50);
    --color-orange-400: oklch(80% 0.15 50);
    --color-orange-500: oklch(75% 0.19 50);
    --color-orange-600: oklch(68% 0.2 50);
    --color-orange-700: oklch(60% 0.18 50);
    --color-orange-800: oklch(52% 0.16 50);
    --color-orange-900: oklch(45% 0.14 50);
    --color-orange-950: oklch(36% 0.12 50);

    /* Green Palette */
    --color-green-50: oklch(97% 0.02 140);
    --color-green-100: oklch(94% 0.04 140);
    --color-green-200: oklch(90% 0.07 140);
    --color-green-300: oklch(86% 0.11 140);
    --color-green-400: oklch(79% 0.15 140);
    --color-green-500: oklch(73% 0.18 140);
    --color-green-600: oklch(66% 0.19 140);
    --color-green-700: oklch(58% 0.18 140);
    --color-green-800: oklch(50% 0.16 140);
    --color-green-900: oklch(43% 0.14 140);
    --color-green-950: oklch(35% 0.12 140);

    /* Yellow Palette */
    --color-yellow-50: oklch(98% 0.02 90);
    --color-yellow-100: oklch(95% 0.04 90);
    --color-yellow-200: oklch(92% 0.07 90);
    --color-yellow-300: oklch(88% 0.12 90);
    --color-yellow-400: oklch(84% 0.16 90);
    --color-yellow-500: oklch(80% 0.19 90);
    --color-yellow-600: oklch(74% 0.19 90);
    --color-yellow-700: oklch(67% 0.18 90);
    --color-yellow-800: oklch(59% 0.16 90);
    --color-yellow-900: oklch(52% 0.14 90);
    --color-yellow-950: oklch(43% 0.12 90);

    /* Violet/Purple Palette */
    --color-violet-50: oklch(97% 0.02 280);
    --color-violet-100: oklch(94% 0.04 280);
    --color-violet-200: oklch(90% 0.08 280);
    --color-violet-300: oklch(86% 0.12 280);
    --color-violet-400: oklch(80% 0.16 280);
    --color-violet-500: oklch(73% 0.2 280);
    --color-violet-600: oklch(66% 0.22 280);
    --color-violet-700: oklch(57% 0.21 280);
    --color-violet-800: oklch(49% 0.18 280);
    --color-violet-900: oklch(42% 0.15 280);
    --color-violet-950: oklch(33% 0.12 280);

    /* Cyan Palette */
    --color-cyan-50: oklch(97.17% 0.023 191.88);
    --color-cyan-100: oklch(94.94% 0.043 191.85);
    --color-cyan-200: oklch(90.56% 0.08 191.74);
    --color-cyan-300: oklch(86.34% 0.111 191.7);
    --color-cyan-400: oklch(81.11% 0.142 191.49);
    --color-cyan-500: oklch(74.99% 0.17 191.24); /* Bright Cyan */
    --color-cyan-600: oklch(68.49% 0.18 191.12);
    --color-cyan-700: oklch(60.59% 0.174 191.19);
    --color-cyan-800: oklch(52.9% 0.155 191.39);
    --color-cyan-900: oklch(46.12% 0.134 191.65);
    --color-cyan-950: oklch(38.05% 0.109 192.15);

    /* Magenta Palette */
    --color-magenta-50: oklch(97.23% 0.022 332.24);
    --color-magenta-100: oklch(94.76% 0.045 331.74);
    --color-magenta-200: oklch(90.16% 0.087 330.93);
    --color-magenta-300: oklch(85.58% 0.125 330.46);
    --color-magenta-400: oklch(79.94% 0.166 330.11);
    --color-magenta-500: oklch(73.03% 0.208 329.84); /* Bright Magenta */
    --color-magenta-600: oklch(65.75% 0.224 329.65);
    --color-magenta-700: oklch(57.27% 0.21 329.59);
    --color-magenta-800: oklch(49.0% 0.181 329.65);
    --color-magenta-900: oklch(41.61% 0.152 329.78);
    --color-magenta-950: oklch(33.76% 0.123 329.98);

    /* Lime Palette */
    --color-lime-50: oklch(98.41% 0.021 131.0);
    --color-lime-100: oklch(97.16% 0.04 131.0);
    --color-lime-200: oklch(94.89% 0.075 131.0);
    --color-lime-300: oklch(92.69% 0.107 131.0);
    --color-lime-400: oklch(90.08% 0.142 131.0);
    --color-lime-500: oklch(87.1% 0.178 131.0); /* Bright Lime */
    --color-lime-600: oklch(81.16% 0.19 131.0);
    --color-lime-700: oklch(73.61% 0.184 131.0);
    --color-lime-800: oklch(66.17% 0.166 131.0);
    --color-lime-900: oklch(58.75% 0.145 131.0);
    --color-lime-950: oklch(49.82% 0.124 131.0);

    /* Chart Colors */
    --color-chart-1-light: oklch(81% 0.16 35);
    --color-chart-2-light: oklch(79% 0.15 180);
    --color-chart-3-light: oklch(68% 0.12 197);
    --color-chart-4-light: oklch(83% 0.14 75);
    --color-chart-5-light: oklch(84% 0.14 35);

    --color-chart-1-dark: oklch(67% 0.14 220);
    --color-chart-2-dark: oklch(70% 0.12 160);
    --color-chart-3-dark: oklch(72% 0.15 30);
    --color-chart-4-dark: oklch(74% 0.13 280);
    --color-chart-5-dark: oklch(75% 0.14 340);

    /* Dashboard Sidebar Styling */
    /* These values are based on the dashboard sidebar component */

    /* Base */
    --sidebar-width: 70px; /* Fixed sidebar width */

    /* Background & Border */
    --sidebar-bg: #ffffff; /* Light mode background */
    --sidebar-bg-dark: hsl(var(--background)); /* Dark mode background from theme */
    --sidebar-border: hsl(var(--border)); /* Border color from theme */

    /* Item Styling */
    --sidebar-item-size: 40px;
    --sidebar-item-bg-active: #f2f1ef;
    --sidebar-item-bg-active-dark: hsl(var(--secondary));
    --sidebar-item-border-active: #dcdad2;
    --sidebar-item-border-active-dark: #2c2c2c;
    --sidebar-item-hover-bg: hsl(var(--accent));
    --sidebar-item-hover-border: #dcdad2;
    --sidebar-item-hover-border-dark: #2c2c2c;

    /* Text & Icon Colors */
    --sidebar-text: #000000; /* Light mode text */
    --sidebar-text-dark: #666666; /* Dark mode muted text */
    --sidebar-text-active-dark: #ffffff; /* Dark mode active text */
    --sidebar-icon-color: #000000; /* Light mode */
    --sidebar-icon-color-dark: #666666; /* Dark mode */
    --sidebar-icon-color-active: var(--primary); /* Active state */
    --sidebar-icon-size: 20px; /* Standard icon size */

    /* Team/Avatar section */
    --sidebar-avatar-size: 32px;
    --sidebar-avatar-border: #dcdad2;
    --sidebar-avatar-border-dark: #2c2c2c;

    /* --- Old Sidebar Variables (kept for compatibility) --- */
    /* Base Background */
    --base-bg: hsl(0, 0%, 5%); /* #0C0C0C */

    /* Backgrounds - Direct HSL values */
    /* --sidebar-bg: hsl(0, 0%, 7%); */ /* #121212 - replaced by new variable above */
    --sidebar-dropdown-bg: hsl(0, 0%, 8%); /* #151515 */
    --sidebar-button-bg: hsl(0, 0%, 11%); /* #1D1D1D */
    --sidebar-button-hover: hsl(0, 0%, 14%); /* #232323 */
    --sidebar-button-hover-bg: hsl(0, 0%, 9%); /* #181818 */
    --sidebar-dropdown-header: hsl(0, 0%, 8%); /* #151515 */
    --sidebar-header-bg: hsl(0, 0%, 7%); /* #121212 */
    --sidebar-group-button-bg: hsl(0, 0%, 11%); /* #1D1D1D */

    /* Borders - Direct HSL values */
    /* --sidebar-border: hsl(0, 0%, 17%); */ /* #2C2C2C - replaced by new variable above */
    --sidebar-dropdown-border: hsl(0, 0%, 17%); /* #2C2C2C */
    --sidebar-button-hover-outline: hsl(45, 13%, 85%); /* #DCDAD2 */
    --sidebar-header-border: hsl(0, 0%, 17%); /* #2C2C2C */
    --sidebar-group-button-border: hsl(0, 0%, 17%); /* #2C2C2C */

    /* Text + Icon Colors */
    --sidebar-foreground: hsl(60, 14%, 96%); /* #F5F5F3 */
    --text-primary: hsl(60, 14%, 96%); /* #F5F5F3 */
    --text-secondary: hsl(0, 0%, 53%); /* #878787 */
    --text-muted: hsl(0, 0%, 44%); /* #707070 */
    --sidebar-text-muted: hsl(0, 0%, 53%); /* #878787 */
    --sidebar-text-hover: hsl(0, 0%, 100%); /* #fff */
    --sidebar-icon: hsl(0, 0%, 53%); /* #878787 */

    /* Accent */
    --sidebar-accent: hsl(201, 100%, 37%); /* #0079BF */
    --sidebar-accent-foreground: #ffffff;
    --sidebar-accent-hover: hsl(203, 89%, 58%); /* #1da1f2 */

    /* Badges */
    --sidebar-badge-red: hsl(344, 81%, 50%); /* #EB144C */
    --sidebar-badge-gold: hsl(48, 100%, 59%); /* #FFD02B */

    /* Effects */
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --inner-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.2);

    /* Header Backgrounds */
    --header-bg: var(--sidebar-bg); /* hsl(0, 0%, 7%) #121212 */
    --header-bg-blur: hsla(0, 0%, 7%, 0.85); /* #121212 with opacity for blur effect */
    --header-border: var(--sidebar-border); /* hsl(0, 0%, 17%) #2C2C2C */
    --header-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* Header Text + Icon Colors */
    --header-foreground: var(--sidebar-foreground); /* hsl(60, 14%, 96%) #F5F5F3 */
    --header-text-primary: var(--sidebar-foreground); /* hsl(60, 14%, 96%) #F5F5F3 */
    --header-text-secondary: var(--sidebar-text-muted); /* hsl(0, 0%, 53%) #878787 */
    --header-text-muted: var(--text-muted); /* hsl(0, 0%, 44%) #707070 */
    --header-icon: var(--sidebar-icon); /* hsl(0, 0%, 53%) #878787 */
    --header-icon-active: var(--sidebar-accent); /* hsl(201, 100%, 37%) #0079BF */

    /* Header Accent */
    --header-accent: var(--sidebar-accent); /* hsl(201, 100%, 37%) #0079BF */
    --header-accent-foreground: #ffffff;
    --header-accent-hover: var(--sidebar-accent-hover); /* hsl(203, 89%, 58%) #1da1f2 */

    /* Header Effects */
    --header-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
    --header-inner-shadow: inset 0 -1px 4px rgba(0, 0, 0, 0.1);

    /* --- Modal Dialog System Variables --- */
    /* Background Colors */
    --background: #fafaf9;
    --background-dark: #121212;
    --background-popup: rgba(250, 250, 249, 0.99);
    --background-popup-dark: rgba(21, 21, 21, 0.99);

    /* Border Colors */
    --border-color: #dcdad2;
    --border-color-dark: #2c2c2c;

    /* Text Colors (reusing existing where applicable) */
    --text-primary-light: #000000; /* For light mode */
    --text-primary-dark: #ffffff; /* For dark mode */
    --text-secondary-light: #606060; /* For light mode */
    --text-secondary-dark: #878787; /* For dark mode */

    /* Interactive Colors */
    --accent: #f2f1ef;
    --accent-dark: #1a1a1a;
    --primary: #0099ff; /* Maps to sidebar-accent */
    --primary-hover: #007acc;
    --destructive: #ef4444; /* Maps to form-invalid-indicator */

    /* Overlay Colors */
    --overlay-light: rgba(246, 246, 243, 0.6);
    --overlay-dark: rgba(0, 0, 0, 0.6);

    /* Font Sizes (mapping to existing where applicable) */
    --font-size-xs: 0.75rem; /* 12px, matches --text-xs */
    --font-size-sm: 0.875rem; /* 14px, matches --text-sm */
    --font-size-base: 1rem; /* 16px, matches --text-base */
    --font-size-lg: 1.125rem; /* 18px, matches --text-lg */
    --font-size-xl: 1.25rem; /* 20px, matches --text-xl */
    --font-size-2xl: 1.5rem; /* 24px, matches --text-2xl */

    /* Font Weights */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;

    /* Modal/Dialog Component Sizing */
    --modal-sm: 24rem; /* 384px */
    --modal-md: 28rem; /* 448px */
    --modal-lg: 32rem; /* 512px */
    --modal-xl: 36rem; /* 576px */
    --modal-2xl: 42rem; /* 672px */
    --modal-full: 100%;

    /* Heights (some match existing form- variables) */
    --input-height: 2.25rem; /* 36px (h-9) */
    --button-height: 2.25rem; /* 36px (h-9) */
    --button-height-lg: 2.5rem; /* 40px (h-10) */
    --header-height: 4.375rem; /* 70px */

    /* Modal Dialog-specific border radius values */
    --modal-radius-sm: 0.125rem; /* 2px */
    --modal-radius: 0.5rem; /* 8px */
  }
}
