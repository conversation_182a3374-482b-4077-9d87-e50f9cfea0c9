{"$schema": "https://turborepo.org/schema.json", "globalDependencies": ["**/.env"], "ui": "stream", "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"env": ["SUPABASE_SERVICE_KEY", "SUPABASE_API_KEY", "RESEND_API_KEY", "RESEND_AUDIENCE_ID", "GOCARDLESS_SECRET_ID", "GOCARDLESS_SECRET_KEY", "UPSTASH_REDIS_REST_URL", "UPSTASH_REDIS_REST_TOKEN", "NOVU_API_KEY", "DUB_API_KEY", "API_ROUTE_SECRET", "TELLER_CERTIFICATE", "TELLER_CERTIFICATE_PRIVATE_KEY", "ENGINE_API_KEY", "PLAID_CLIENT_ID", "PLAID_SECRET", "GITHUB_TOKEN", "PLAIN_API_KEY", "BASELIME_SERVICE", "BASELIME_API_KEY", "OPENAI_API_KEY", "MISTRAL_API_KEY", "OPENPANEL_SECRET_KEY", "SLACK_CLIENT_SECRET", "SLACK_CLIENT_ID", "SLACK_SIGNING_SECRET", "GMAIL_CLIENT_ID", "GMAIL_CLIENT_SECRET", "GMAIL_REDIRECT_URI", "OUTLOOK_CLIENT_ID", "OUTLOOK_CLIENT_SECRET", "OUTLOOK_REDIRECT_URI", "MIDDAY_ENCRYPTION_KEY", "NEXT_PUBLIC_URL", "VERCEL_TARGET_ENV"], "inputs": ["$TURBO_DEFAULT$", ".env"], "dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "next-env.d.ts", ".expo/**", "dist/**", "build/**", "lib/**"]}, "start": {"cache": false}, "test": {"cache": false}, "dev": {"inputs": ["$TURBO_DEFAULT$", ".env"], "persistent": true, "cache": false}, "jobs": {"persistent": true, "cache": false}, "format": {}, "lint": {"dependsOn": ["^topo"]}, "typecheck": {"dependsOn": ["^topo"], "outputs": []}}}