{"compilerOptions": {"lib": ["ESNext"], "target": "ESNext", "module": "ESNext", "moduleDetection": "force", "allowJs": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "verbatimModuleSyntax": true, "noEmit": true, "strict": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": false, "noUnusedParameters": false, "noPropertyAccessFromIndexSignature": false, "jsx": "react-jsx", "jsxImportSource": "react", "baseUrl": ".", "paths": {"@api/*": ["src/*"], "@engine/*": ["../engine/src/*"], "@jobs/*": ["../../packages/jobs/src/*"]}, "types": ["bun-types"]}, "exclude": ["node_modules"], "include": ["src/**/*.ts"]}