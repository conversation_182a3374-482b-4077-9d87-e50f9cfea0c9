import { ArchiveIcon } from "@radix-ui/react-icons";
import { FaXTwitter } from "react-icons/fa6";
import { FiGithub } from "react-icons/fi";
import {
  MdArrowBack,
  MdArrowDownward,
  MdArrowRightAlt,
  MdArrowUpward,
  MdAttachMoney,
  MdAutoAwesome,
  MdChangeHistory,
  MdChevronLeft,
  MdChevronRight,
  MdClose,
  MdDescription,
  MdDownloading,
  MdDragIndicator,
  MdErrorOutline,
  MdExpandLess,
  MdExpandMore,
  MdFolder,
  MdFolderZip,
  MdInventory2,
  MdIosShare,
  MdMenu,
  MdMoreHoriz,
  MdOutlineAccountBalance,
  MdOutlineAccountCircle,
  MdOutlineAdd,
  MdOutlineArrowOutward,
  MdOutlineAssuredWorkload,
  MdOutlineAttachFile,
  MdOutlineAttachMoney,
  MdOutlineAutoAwesome,
  MdOutlineBrokenImage,
  Md<PERSON>utlineCalculate,
  MdOutlineCalendarMonth,
  MdOutlineCategory,
  MdOutlineClear,
  MdOutlineConfirmationNumber,
  MdOutlineContentCopy,
  MdOutlineCropFree,
  MdOutlineEditNote,
  MdOutlineEmail,
  MdOutlineEqualizer,
  MdOutlineFace,
  MdOutlineFactCheck,
  MdOutlineFilterList,
  MdOutlineGridView,
  MdOutlineInventory2,
  MdOutlineLaunch,
  MdOutlineMoreTime,
  MdOutlineMoreVert,
  MdOutlineNotificationsNone,
  MdOutlineOpenInNew,
  MdOutlinePalette,
  MdOutlinePictureAsPdf,
  MdOutlineQrCode2,
  MdOutlineReorder,
  MdOutlineRepeat,
  MdOutlineSquareFoot,
  MdOutlineStyle,
  MdOutlineSubdirectoryArrowLeft,
  MdOutlineTask,
  MdOutlineTune,
  MdOutlineVisibility,
  MdOutlineVolumeOff,
  MdOutlineVolumeUp,
  MdPictureAsPdf,
  MdPlayArrow,
  MdRefresh,
  MdSearch,
  MdSnippetFolder,
  MdSort,
  MdTrendingDown,
  MdTrendingUp,
} from "react-icons/md";
import { PiDiscordLogo } from "react-icons/pi";

type SVGIconProps = {
  size?: number;
  stroke?: string;
  fill?: string;
  strokeWidth?: number;
  className?: string;
  children?: React.ReactNode;
  viewBox?: string;
};

const SVGIcon: React.FC<SVGIconProps> = ({
  size = 20,
  stroke = "currentColor",
  fill = "currentColor",
  strokeWidth = 0.25,
  className,
  children,
  viewBox,
}) => {
  const intrinsicContentDimension = 20;
  const defaultViewBox = `0 0 ${intrinsicContentDimension} ${intrinsicContentDimension}`;

  return (
    <svg
      width={size}
      height={size}
      viewBox={viewBox || defaultViewBox}
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {children}
    </svg>
  );
};

export const Icons = {
  LogoSmall: (props: SVGIconProps) => (
    <SVGIcon size={28} {...props} viewBox="0 0 28 28">
      <path
        fill="currentColor"
        d="M14.854 2.698a9.148 9.148 0 0 1 0 5.786l-.542 1.623 2.012-1.783a7.378 7.378 0 0 0 2.333-4.04l.57-2.786 1.733.354-.57 2.787a9.149 9.149 0 0 1-2.892 5.01l-1.283 1.137 2.635-.538a7.379 7.379 0 0 0 4.04-2.333l1.888-2.129 1.324 1.174-1.887 2.129a9.148 9.148 0 0 1-5.01 2.892l-1.68.344 2.551.85a7.379 7.379 0 0 0 4.666 0l2.698-.9.56 1.68-2.698.9a9.148 9.148 0 0 1-5.785 0l-1.625-.543 1.784 2.012a7.375 7.375 0 0 0 4.04 2.331l2.787.572-.355 1.733-2.787-.57a9.148 9.148 0 0 1-5.01-2.892l-1.136-1.281.539 2.633a7.376 7.376 0 0 0 2.331 4.04l2.129 1.887L21.04 26.1l-2.129-1.887a9.146 9.146 0 0 1-2.892-5.01l-.343-1.677-.85 2.55a7.379 7.379 0 0 0 0 4.665l.9 2.698-1.68.56-.9-2.698a9.148 9.148 0 0 1 0-5.785l.541-1.627-2.01 1.785a7.38 7.38 0 0 0-2.334 4.04l-.57 2.788-1.733-.357.57-2.785a9.148 9.148 0 0 1 2.892-5.01l1.281-1.138-2.633.54a7.377 7.377 0 0 0-4.04 2.332l-1.887 2.129L1.9 21.04l1.887-2.129a9.146 9.146 0 0 1 5.01-2.892l1.678-.345-2.55-.849a7.379 7.379 0 0 0-4.666 0l-2.698.9-.56-1.68 2.698-.9a9.148 9.148 0 0 1 5.786 0l1.623.542-1.783-2.01a7.377 7.377 0 0 0-4.04-2.334l-2.786-.57.354-1.733 2.787.57a9.148 9.148 0 0 1 5.01 2.892l1.135 1.28-.538-2.632a7.376 7.376 0 0 0-2.331-4.04L5.786 3.223 6.96 1.898 9.09 3.785a9.148 9.148 0 0 1 2.892 5.01l.344 1.68.85-2.551a7.379 7.379 0 0 0 0-4.666l-.9-2.698 1.68-.56.9 2.698ZM14 11.234A2.767 2.767 0 0 0 11.234 14l.015.283a2.766 2.766 0 0 0 5.502 0l.014-.283-.014-.283a2.766 2.766 0 0 0-2.468-2.468L14 11.234Z"
      />
    </SVGIcon>
  ),
  Logo: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={113}
      height={32}
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        d="M16.622 3.866a9.821 9.821 0 0 1 0 6.211l-.582 1.743 2.16-1.914a7.92 7.92 0 0 0 2.505-4.337l.612-2.992 1.861.381-.612 2.992a9.822 9.822 0 0 1-3.106 5.38l-1.376 1.22 2.828-.579a7.923 7.923 0 0 0 4.338-2.504l2.027-2.285 1.421 1.26-2.026 2.285a9.821 9.821 0 0 1-5.379 3.106l-1.804.368 2.74.913a7.921 7.921 0 0 0 5.009 0l2.897-.965.6 1.802-2.896.966a9.821 9.821 0 0 1-6.211 0l-1.744-.582 1.915 2.16a7.919 7.919 0 0 0 4.337 2.503l2.992.613-.38 1.862-2.993-.612a9.822 9.822 0 0 1-5.379-3.106l-1.22-1.375.58 2.827a7.92 7.92 0 0 0 2.503 4.337l2.284 2.026-1.26 1.422-2.285-2.026a9.82 9.82 0 0 1-3.105-5.378l-.369-1.802-.912 2.737a7.923 7.923 0 0 0 0 5.01l.966 2.897-1.804.6-.966-2.896a9.821 9.821 0 0 1 0-6.211l.581-1.747-2.159 1.917a7.922 7.922 0 0 0-2.504 4.338l-.612 2.992-1.862-.382.612-2.991a9.822 9.822 0 0 1 3.106-5.38l1.375-1.22-2.827.579a7.92 7.92 0 0 0-4.337 2.504l-2.026 2.285-1.422-1.26 2.027-2.285a9.82 9.82 0 0 1 5.377-3.106l1.803-.37-2.738-.911a7.921 7.921 0 0 0-5.01 0l-2.897.965-.6-1.802 2.897-.966a9.821 9.821 0 0 1 6.21 0l1.743.581-1.914-2.159a7.92 7.92 0 0 0-4.337-2.504l-2.992-.612.381-1.862 2.992.612a9.822 9.822 0 0 1 5.38 3.106l1.217 1.374-.577-2.826a7.919 7.919 0 0 0-2.503-4.337L6.887 4.43l1.26-1.423 2.285 2.026a9.822 9.822 0 0 1 3.106 5.38l.368 1.802.913-2.738a7.921 7.921 0 0 0 0-5.01l-.965-2.897 1.803-.6.965 2.896Zm-.917 9.165A2.97 2.97 0 0 0 12.735 16l.017.304a2.97 2.97 0 0 0 5.906 0l.015-.304-.015-.304a2.97 2.97 0 0 0-2.65-2.65l-.303-.015ZM102.037 10.584h1.906l3.489 9.576 3.358-9.576h1.885l-4.658 12.934c-.434 1.235-1.257 1.798-2.557 1.798h-1.365v-1.581h1.192c.585 0 .931-.217 1.148-.824l.412-1.061h-.607l-4.203-11.266ZM90.86 13.963c.456-2.231 2.233-3.64 4.68-3.64 2.969 0 4.615 1.734 4.615 4.81v4.615c0 .52.217.737.715.737h.455v1.581h-.758c-1.17 0-2.188-.39-2.166-1.798-.499 1.083-1.799 2.058-3.619 2.058-2.275 0-4.116-1.213-4.116-3.25 0-2.361 1.798-2.968 4.311-3.466l3.402-.65c-.022-1.993-.953-2.947-2.838-2.947-1.474 0-2.448.759-2.795 2.102l-1.885-.152Zm1.67 5.114c0 .931.8 1.69 2.49 1.668 1.907 0 3.402-1.344 3.402-4.008v-.195l-2.751.476c-1.712.304-3.142.434-3.142 2.058ZM87.816 6.684v15.382h-1.603l-.065-1.69c-.65 1.214-1.885 1.95-3.662 1.95-3.315 0-4.853-2.816-4.853-6.001 0-3.185 1.538-6.002 4.853-6.002 1.69 0 2.903.65 3.553 1.82v-5.46h1.777Zm-8.32 9.64c0 2.232.954 4.312 3.359 4.312 2.361 0 3.336-2.123 3.336-4.311 0-2.275-.975-4.377-3.336-4.377-2.405 0-3.359 2.08-3.359 4.377ZM74.783 6.684v15.382H73.18l-.065-1.69c-.65 1.214-1.885 1.95-3.661 1.95-3.315 0-4.853-2.816-4.853-6.001 0-3.185 1.538-6.002 4.853-6.002 1.69 0 2.903.65 3.553 1.82v-5.46h1.776Zm-8.32 9.64c0 2.232.954 4.312 3.359 4.312 2.361 0 3.336-2.123 3.336-4.311 0-2.275-.975-4.377-3.336-4.377-2.405 0-3.358 2.08-3.358 4.377ZM59.964 10.584h1.776v11.482h-1.776V10.584Zm-.044-1.799V6.727h1.863v2.058H59.92ZM41.346 10.584h1.625l.065 1.95c.52-1.409 1.711-2.21 3.314-2.21 1.647 0 2.839.888 3.337 2.426.498-1.56 1.712-2.426 3.51-2.426 2.296 0 3.64 1.603 3.64 4.354v7.388H55.06v-6.868c0-2.101-.823-3.293-2.232-3.293-1.798 0-2.838 1.213-2.838 3.315v6.846h-1.776v-6.868c0-2.058-.845-3.293-2.232-3.293-1.776 0-2.86 1.257-2.86 3.293v6.868h-1.776V10.584Z"
      />
    </svg>
  ),
  Overview: (props?: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M2.70898 17.2916V15.9214L3.95898 14.6714V17.2916H2.70898ZM6.04232 17.2916V12.5881L7.29232 11.3381V17.2916H6.04232ZM9.37565 17.2916V11.3381L10.6257 12.6089V17.2916H9.37565ZM12.709 17.2916V12.6089L13.959 11.3589V17.2916H12.709ZM16.0423 17.2916V9.25476L17.2923 8.00476V17.2916H16.0423ZM2.70898 12.6827V10.9214L8.33398 5.29643L11.6673 8.62976L17.2923 3.00476V4.76601L11.6673 10.391L8.33398 7.05768L2.70898 12.6827Z" />
    </SVGIcon>
  ),
  Apps: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M4.99936 16.3461C4.62922 16.3461 4.31235 16.2143 4.04874 15.9507C3.78513 15.687 3.65332 15.3702 3.65332 15C3.65332 14.6299 3.78513 14.313 4.04874 14.0494C4.31235 13.7858 4.62922 13.654 4.99936 13.654C5.3695 13.654 5.68638 13.7858 5.94999 14.0494C6.2136 14.313 6.3454 14.6299 6.3454 15C6.3454 15.3702 6.2136 15.687 5.94999 15.9507C5.68638 16.2143 5.3695 16.3461 4.99936 16.3461ZM9.99936 16.3461C9.62922 16.3461 9.31235 16.2143 9.04874 15.9507C8.78513 15.687 8.65332 15.3702 8.65332 15C8.65332 14.6299 8.78513 14.313 9.04874 14.0494C9.31235 13.7858 9.62922 13.654 9.99936 13.654C10.3695 13.654 10.6864 13.7858 10.95 14.0494C11.2136 14.313 11.3454 14.6299 11.3454 15C11.3454 15.3702 11.2136 15.687 10.95 15.9507C10.6864 16.2143 10.3695 16.3461 9.99936 16.3461ZM14.9994 16.3461C14.6292 16.3461 14.3123 16.2143 14.0487 15.9507C13.7851 15.687 13.6533 15.3702 13.6533 15C13.6533 14.6299 13.7851 14.313 14.0487 14.0494C14.3123 13.7858 14.6292 13.654 14.9994 13.654C15.3695 13.654 15.6864 13.7858 15.95 14.0494C16.2136 14.313 16.3454 14.6299 16.3454 15C16.3454 15.3702 16.2136 15.687 15.95 15.9507C15.6864 16.2143 15.3695 16.3461 14.9994 16.3461ZM4.99936 11.3461C4.62922 11.3461 4.31235 11.2143 4.04874 10.9507C3.78513 10.687 3.65332 10.3702 3.65332 10C3.65332 9.62989 3.78513 9.31302 4.04874 9.04941C4.31235 8.7858 4.62922 8.65399 4.99936 8.65399C5.3695 8.65399 5.68638 8.7858 5.94999 9.04941C6.2136 9.31302 6.3454 9.62989 6.3454 10C6.3454 10.3702 6.2136 10.687 5.94999 10.9507C5.68638 11.2143 5.3695 11.3461 4.99936 11.3461ZM9.99936 11.3461C9.62922 11.3461 9.31235 11.2143 9.04874 10.9507C8.78513 10.687 8.65332 10.3702 8.65332 10C8.65332 9.62989 8.78513 9.31302 9.04874 9.04941C9.31235 8.7858 9.62922 8.65399 9.99936 8.65399C10.3695 8.65399 10.6864 8.7858 10.95 9.04941C11.2136 9.31302 11.3454 9.62989 11.3454 10C11.3454 10.3702 11.2136 10.687 10.95 10.9507C10.6864 11.2143 10.3695 11.3461 9.99936 11.3461ZM14.9994 11.3461C14.6292 11.3461 14.3123 11.2143 14.0487 10.9507C13.7851 10.687 13.6533 10.3702 13.6533 10C13.6533 9.62989 13.7851 9.31302 14.0487 9.04941C14.3123 8.7858 14.6292 8.65399 14.9994 8.65399C15.3695 8.65399 15.6864 8.7858 15.95 9.04941C16.2136 9.31302 16.3454 9.62989 16.3454 10C16.3454 10.3702 16.2136 10.687 15.95 10.9507C15.6864 11.2143 15.3695 11.3461 14.9994 11.3461ZM4.99936 6.34607C4.62922 6.34607 4.31235 6.21427 4.04874 5.95066C3.78513 5.68705 3.65332 5.37017 3.65332 5.00003C3.65332 4.62989 3.78513 4.31302 4.04874 4.04941C4.31235 3.7858 4.62922 3.65399 4.99936 3.65399C5.3695 3.65399 5.68638 3.7858 5.94999 4.04941C6.2136 4.31302 6.3454 4.62989 6.3454 5.00003C6.3454 5.37017 6.2136 5.68705 5.94999 5.95066C5.68638 6.21427 5.3695 6.34607 4.99936 6.34607ZM9.99936 6.34607C9.62922 6.34607 9.31235 6.21427 9.04874 5.95066C8.78513 5.68705 8.65332 5.37017 8.65332 5.00003C8.65332 4.62989 8.78513 4.31302 9.04874 4.04941C9.31235 3.7858 9.62922 3.65399 9.99936 3.65399C10.3695 3.65399 10.6864 3.7858 10.95 4.04941C11.2136 4.31302 11.3454 4.62989 11.3454 5.00003C11.3454 5.37017 11.2136 5.68705 10.95 5.95066C10.6864 6.21427 10.3695 6.34607 9.99936 6.34607ZM14.9994 6.34607C14.6292 6.34607 14.3123 6.21427 14.0487 5.95066C13.7851 5.68705 13.6533 5.37017 13.6533 5.00003C13.6533 4.62989 13.7851 4.31302 14.0487 4.04941C14.3123 3.7858 14.6292 3.65399 14.9994 3.65399C15.3695 3.65399 15.6864 3.7858 15.95 4.04941C16.2136 4.31302 16.3454 4.62989 16.3454 5.00003C16.3454 5.37017 16.2136 5.68705 15.95 5.95066C15.6864 6.21427 15.3695 6.34607 14.9994 6.34607Z" />
    </SVGIcon>
  ),
  Transactions: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M5.97685 13.9743H7.45122V12.5H5.97685V13.9743ZM5.97685 10.737H7.45122V9.26288H5.97685V10.737ZM5.97685 7.49996H7.45122V6.02558H5.97685V7.49996ZM9.24622 13.862H13.9898V12.6123H9.24622V13.862ZM9.24622 10.625H13.9898V9.37496H9.24622V10.625ZM9.24622 7.38767H13.9898V6.13788H9.24622V7.38767ZM2.91602 17.0833V2.91663H17.0827V17.0833H2.91602ZM4.16602 15.8333H15.8327V4.16663H4.16602V15.8333Z" />
    </SVGIcon>
  ),
  Invoice: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M6.875 14.7916H13.125V13.5416H6.875V14.7916ZM6.875 11.4583H13.125V10.2083H6.875V11.4583ZM3.75 17.9166V2.08331H11.875L16.25 6.45831V17.9166H3.75ZM11.25 7.08331V3.33331H5V16.6666H15V7.08331H11.25Z" />
    </SVGIcon>
  ),
  Vault: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M9.99967 12.7083C10.4045 12.7083 10.7488 12.5665 11.0326 12.2829C11.3162 11.9992 11.458 11.6549 11.458 11.25C11.458 10.8451 11.3162 10.5008 11.0326 10.2171C10.7488 9.93347 10.4045 9.79167 9.99967 9.79167C9.59481 9.79167 9.25051 9.93347 8.96676 10.2171C8.68315 10.5008 8.54134 10.8451 8.54134 11.25C8.54134 11.6549 8.68315 11.9992 8.96676 12.2829C9.25051 12.5665 9.59481 12.7083 9.99967 12.7083ZM2.08301 17.0833V5.41667H7.08301V2.5H12.9163V5.41667H17.9163V17.0833H2.08301ZM3.33301 15.8333H16.6663V6.66667H3.33301V15.8333ZM8.33301 5.41667H11.6663V3.75H8.33301V5.41667Z" />
    </SVGIcon>
  ),
  Customers: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M14.0702 16.9871C13.2681 16.9871 12.5815 16.7015 12.0104 16.1304C11.4393 15.5592 11.1537 14.8725 11.1537 14.0704C11.1537 13.2683 11.4393 12.5817 12.0104 12.0106C12.5815 11.4395 13.2681 11.1539 14.0702 11.1539C14.8723 11.1539 15.5589 11.4395 16.1302 12.0106C16.7013 12.5817 16.9869 13.2683 16.9869 14.0704C16.9869 14.8725 16.7013 15.5592 16.1302 16.1304C15.5589 16.7015 14.8723 16.9871 14.0702 16.9871ZM14.0702 15.7371C14.5285 15.7371 14.9209 15.5739 15.2473 15.2475C15.5737 14.9211 15.7369 14.5287 15.7369 14.0704C15.7369 13.6121 15.5737 13.2197 15.2473 12.8933C14.9209 12.5669 14.5285 12.4037 14.0702 12.4037C13.6119 12.4037 13.2195 12.5669 12.8931 12.8933C12.5667 13.2197 12.4035 13.6121 12.4035 14.0704C12.4035 14.5287 12.5667 14.9211 12.8931 15.2475C13.2195 15.5739 13.6119 15.7371 14.0702 15.7371ZM5.92936 14.4871C5.12728 14.4871 4.44061 14.2015 3.86936 13.6304C3.29825 13.0592 3.0127 12.3725 3.0127 11.5704C3.0127 10.7683 3.29825 10.0817 3.86936 9.51061C4.44061 8.9395 5.12728 8.65395 5.92936 8.65395C6.73145 8.65395 7.41804 8.9395 7.98915 9.51061C8.56027 10.0817 8.84582 10.7683 8.84582 11.5704C8.84582 12.3725 8.56027 13.0592 7.98915 13.6304C7.41804 14.2015 6.73145 14.4871 5.92936 14.4871ZM5.92936 13.2371C6.3877 13.2371 6.78006 13.0739 7.10645 12.7475C7.43283 12.4211 7.59603 12.0287 7.59603 11.5704C7.59603 11.1121 7.43283 10.7197 7.10645 10.3933C6.78006 10.0669 6.3877 9.90374 5.92936 9.90374C5.47103 9.90374 5.07867 10.0669 4.75228 10.3933C4.42589 10.7197 4.2627 11.1121 4.2627 11.5704C4.2627 12.0287 4.42589 12.4211 4.75228 12.7475C5.07867 13.0739 5.47103 13.2371 5.92936 13.2371ZM9.16645 8.07686C8.36436 8.07686 7.67777 7.79131 7.10665 7.2202C6.5354 6.64895 6.24978 5.96228 6.24978 5.1602C6.24978 4.35811 6.5354 3.67152 7.10665 3.10041C7.67777 2.52916 8.36436 2.24353 9.16645 2.24353C9.96853 2.24353 10.6551 2.52916 11.2262 3.10041C11.7975 3.67152 12.0831 4.35811 12.0831 5.1602C12.0831 5.96228 11.7975 6.64895 11.2262 7.2202C10.6551 7.79131 9.96853 8.07686 9.16645 8.07686ZM9.16645 6.82686C9.62478 6.82686 10.0171 6.66367 10.3435 6.33728C10.6699 6.01089 10.8331 5.61853 10.8331 5.1602C10.8331 4.70186 10.6699 4.3095 10.3435 3.98311C10.0171 3.65672 9.62478 3.49353 9.16645 3.49353C8.70811 3.49353 8.31575 3.65672 7.98936 3.98311C7.66297 4.3095 7.49978 4.70186 7.49978 5.1602C7.49978 5.61853 7.66297 6.01089 7.98936 6.33728C8.31575 6.66367 8.70811 6.82686 9.16645 6.82686Z" />
    </SVGIcon>
  ),
  Shortcut: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M3.667 13.5V6h7.418L8.62 3.546l.713-.713L13 6.5l-3.677 3.677-.703-.713L11.085 7H4.667v6.5h-1Z" />
    </SVGIcon>
  ),
  Apple: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={19}
      height={23}
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        d="M18.143 17.645a11.967 11.967 0 0 1-1.183 2.126c-.622.887-1.131 1.5-1.524 1.842-.608.56-1.26.846-1.958.862-.501 0-1.105-.143-1.809-.432-.706-.288-1.354-.43-1.947-.43-.622 0-1.29.142-2.003.43-.714.29-1.29.44-1.73.455-.67.029-1.337-.266-2.002-.885-.426-.371-.957-1.007-1.594-1.907-.683-.961-1.245-2.076-1.685-3.347C.236 14.986 0 13.656 0 12.369c0-1.474.319-2.746.957-3.811A5.612 5.612 0 0 1 2.96 6.53a5.39 5.39 0 0 1 2.71-.765c.531 0 1.228.165 2.095.488.863.324 1.418.489 1.661.489.182 0 .799-.192 1.843-.576.988-.355 1.822-.503 2.505-.445 1.851.15 3.242.88 4.166 2.194-1.655 1.003-2.474 2.408-2.458 4.21.015 1.404.524 2.572 1.525 3.5.454.43.96.763 1.524.999a16.56 16.56 0 0 1-.388 1.02ZM13.898.94c0 1.1-.402 2.128-1.204 3.079-.967 1.13-2.136 1.783-3.404 1.68a3.425 3.425 0 0 1-.026-.417c0-1.056.46-2.186 1.277-3.11.407-.469.926-.858 1.555-1.168.627-.306 1.22-.475 1.778-.504.017.147.024.294.024.44Z"
      />
    </svg>
  ),
  Google: (props: any) => (
    <svg
      width="20"
      height="20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          d="M10 3.958c1.475 0 2.796.509 3.838 1.5l2.854-2.854C14.959.992 12.696 0 10 0a9.995 9.995 0 0 0-8.933 5.508l3.325 2.58c.787-2.371 3-4.13 5.608-4.13Z"
          fill="#585858"
        />
        <path
          d="M19.575 10.23c0-.655-.063-1.288-.158-1.897H10v3.759h5.392a4.648 4.648 0 0 1-1.992 2.991l3.22 2.5c1.88-1.741 2.955-4.316 2.955-7.354Z"
          fill="#878787"
        />
        <path
          d="M4.388 11.912A6.075 6.075 0 0 1 4.07 10c0-.667.112-1.308.317-1.913L1.063 5.508A9.964 9.964 0 0 0 0 10c0 1.617.383 3.142 1.067 4.492l3.32-2.58Z"
          fill="#D7D7D7"
        />
        <path
          d="M10 20c2.7 0 4.97-.887 6.62-2.42l-3.22-2.5c-.896.603-2.05.958-3.4.958-2.608 0-4.82-1.759-5.612-4.13l-3.325 2.58C2.712 17.758 6.091 20 10 20Z"
          fill="#B3B3B3"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="currentColor" d="M0 0h20v20H0z" />
        </clipPath>
      </defs>
    </svg>
  ),
  InboxCustomize: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={17}
      height={17}
      viewBox="0 -960 960 960"
      {...props}
    >
      <path
        fill="currentColor"
        d="M200-160q-33 0-56.5-23.5T120-240v-560q0-33 23.5-56.5T200-880h560q33 0 56.5 23.5T840-800v226q-19-9-39-14.5t-41-8.5v-203H200v360h168q9 27 30 47t47 28q-3 20-4 40.5t2 40.5q-36-7-67.5-26.5T320-360H200v120h253q7 22 16 42t22 38H200Zm0-80h253-253Zm481 120-12-60q-12-5-22.5-10.5T625-204l-58 18-40-68 46-40q-2-12-2-26t2-26l-46-40 40-68 58 18q11-8 21.5-13.5T669-460l12-60h80l12 60q12 5 22.5 10.5T817-436l58-18 40 68-46 40q2 12 2 26t-2 26l46 40-40 68-58-18q-11 8-21.5 13.5T773-180l-12 60h-80Zm40-120q33 0 56.5-23.5T801-320q0-33-23.5-56.5T721-400q-33 0-56.5 23.5T641-320q0 33 23.5 56.5T721-240Z"
      />
    </svg>
  ),
  Import: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={props.size ?? 24}
      height={props.size ?? 24}
      fill="currentColor"
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M160-120v-720h640v400H240v80h200v80H240v80h200v80H160Zm456 0L504-232l56-56 56 56 142-142 56 56-198 198ZM240-520h200v-80H240v80Zm280 0h200v-80H520v80ZM240-680h200v-80H240v80Zm280 0h200v-80H520v80Z" />
    </svg>
  ),
  Settings: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M8.0768 17.9167L7.75951 15.3781C7.53631 15.3034 7.30742 15.1988 7.07284 15.0642C6.83839 14.9295 6.62874 14.7852 6.44388 14.6315L4.09284 15.625L2.16992 12.2917L4.20346 10.7548C4.1843 10.6309 4.17069 10.5065 4.16263 10.3815C4.15457 10.2565 4.15055 10.132 4.15055 10.0079C4.15055 9.88945 4.15457 9.76904 4.16263 9.64668C4.17069 9.52432 4.1843 9.3905 4.20346 9.24522L2.16992 7.70834L4.09284 4.39105L6.43576 5.37668C6.63673 5.21751 6.85124 5.07195 7.0793 4.94001C7.30735 4.80807 7.53145 4.70202 7.75159 4.62188L8.0768 2.08334H11.923L12.2403 4.6298C12.4903 4.72063 12.7165 4.82668 12.9189 4.94793C13.1214 5.06918 13.3258 5.21209 13.532 5.37668L15.907 4.39105L17.8299 7.70834L15.7643 9.26918C15.7942 9.40376 15.8105 9.52959 15.8133 9.64668C15.8159 9.76362 15.8172 9.8814 15.8172 10C15.8172 10.1132 15.8145 10.2283 15.8091 10.3454C15.8038 10.4624 15.7846 10.5962 15.7516 10.7469L17.8012 12.2917L15.878 15.625L13.532 14.6233C13.3258 14.7879 13.1153 14.9335 12.9005 15.06C12.6858 15.1867 12.4658 15.2901 12.2403 15.3702L11.923 17.9167H8.0768ZM9.16659 16.6667H10.8045L11.1041 14.4344C11.5294 14.3233 11.918 14.1654 12.2699 13.9608C12.622 13.7561 12.9615 13.493 13.2885 13.1715L15.3589 14.0417L16.1795 12.625L14.3718 11.2629C14.4412 11.0471 14.4885 10.8355 14.5135 10.6281C14.5386 10.4209 14.5512 10.2115 14.5512 10C14.5512 9.78307 14.5386 9.57369 14.5135 9.37189C14.4885 9.16994 14.4412 8.96369 14.3718 8.75314L16.1953 7.37501L15.3749 5.95834L13.2803 6.84126C13.0015 6.5432 12.6674 6.27987 12.278 6.05126C11.8886 5.82265 11.4946 5.66077 11.0962 5.56564L10.8333 3.33334H9.17951L8.90367 5.55772C8.47853 5.65814 8.08596 5.81195 7.72596 6.01918C7.36583 6.22654 7.02228 6.49362 6.69534 6.82043L4.62492 5.95834L3.80451 7.37501L5.60409 8.71626C5.53464 8.91404 5.48603 9.11973 5.45826 9.33334C5.43048 9.54695 5.41659 9.77182 5.41659 10.0079C5.41659 10.2249 5.43048 10.4375 5.45826 10.6458C5.48603 10.8542 5.53201 11.0599 5.59617 11.2629L3.80451 12.625L4.62492 14.0417L6.68742 13.1667C7.00367 13.4914 7.3418 13.7574 7.7018 13.9648C8.06194 14.172 8.45992 14.3312 8.89576 14.4423L9.16659 16.6667ZM10.0095 12.5C10.7028 12.5 11.2928 12.2567 11.7795 11.77C12.2662 11.2833 12.5095 10.6933 12.5095 10C12.5095 9.30668 12.2662 8.71668 11.7795 8.23001C11.2928 7.74334 10.7028 7.50001 10.0095 7.50001C9.30756 7.50001 8.71541 7.74334 8.23305 8.23001C7.75069 8.71668 7.50951 9.30668 7.50951 10C7.50951 10.6933 7.75069 11.2833 8.23305 11.77C8.71541 12.2567 9.30756 12.5 10.0095 12.5Z" />
    </SVGIcon>
  ),
  Inbox2: (props?: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M2.91602 17.0833V2.91663H17.0827V17.0833H2.91602ZM4.16602 15.8333H15.8327V13.1731H13.0923C12.7291 13.7009 12.278 14.1106 11.7389 14.4023C11.1999 14.6939 10.62 14.8398 9.99935 14.8398C9.37865 14.8398 8.79879 14.6939 8.25977 14.4023C7.72074 14.1106 7.26963 13.7009 6.90643 13.1731H4.16602V15.8333ZM9.99935 13.5898C10.5271 13.5898 11.0063 13.437 11.4368 13.1314C11.8674 12.8259 12.166 12.4231 12.3327 11.9231H15.8327V4.16663H4.16602V11.9231H7.66602C7.83268 12.4231 8.13129 12.8259 8.56185 13.1314C8.9924 13.437 9.47157 13.5898 9.99935 13.5898Z" />
    </SVGIcon>
  ),
  Decimals: ({
    className,
    size = 24,
    ...props
  }: React.SVGProps<SVGSVGElement> & { size?: number | string }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height={size}
      viewBox="0 -960 960 960"
      width={size}
      fill="currentColor"
      className={className}
      {...props}
    >
      <path d="m720-80-56-56 63-64H480v-80h247l-63-64 56-56 160 160L720-80ZM80-440v-120h120v120H80Zm300 0q-58 0-99-41t-41-99v-160q0-58 41-99t99-41q58 0 99 41t41 99v160q0 58-41 99t-99 41Zm360 0q-58 0-99-41t-41-99v-160q0-58 41-99t99-41q58 0 99 41t41 99v160q0 58-41 99t-99 41Zm-360-80q25 0 42.5-17.5T440-580v-160q0-25-17.5-42.5T380-800q-25 0-42.5 17.5T320-740v160q0 25 17.5 42.5T380-520Zm360 0q25 0 42.5-17.5T800-580v-160q0-25-17.5-42.5T740-800q-25 0-42.5 17.5T680-740v160q0 25 17.5 42.5T740-520Z" />
    </svg>
  ),
  Check: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={16}
      height={17}
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        d="m14 5.167-8 8L2.333 9.5l.94-.94L6 11.28l7.06-7.053.94.94Z"
      />
    </svg>
  ),
  AlertCircle: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={16}
      height={17}
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        d="M7.333 10.5h1.334v1.333H7.334V10.5Zm0-5.333h1.334v4H7.334v-4ZM8 1.833c-3.686 0-6.667 3-6.667 6.667A6.667 6.667 0 1 0 8 1.833Zm0 12A5.333 5.333 0 1 1 8 3.167a5.333 5.333 0 0 1 0 10.666Z"
      />
    </svg>
  ),
  Transactions2: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={40}
      height={40}
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        d="M23.333 16.667H5V20h18.333v-3.333Zm0-6.667H5v3.333h18.333V10ZM5 26.667h11.667v-3.334H5v3.334Zm19 10 4.333-4.334 4.334 4.334L35 34.333 30.667 30 35 25.667l-2.333-2.334-4.334 4.334L24 23.333l-2.333 2.334L26 30l-4.333 4.333L24 36.667Z"
      />
    </svg>
  ),
  Info: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={14}
      height={14}
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        d="M6.333 5h1.333V3.667H6.333M7 12.333A5.34 5.34 0 0 1 1.666 7 5.34 5.34 0 0 1 7 1.667 5.34 5.34 0 0 1 12.333 7 5.34 5.34 0 0 1 7 12.333Zm0-12a6.667 6.667 0 1 0 0 13.334A6.667 6.667 0 0 0 7 .333Zm-.667 10h1.333v-4H6.333v4Z"
      />
    </svg>
  ),
  Github: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={22}
      height={22}
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M11.21.22C5.412.22.71 5.038.71 10.984c0 4.757 3.009 8.792 7.18 10.216.525.1.718-.234.718-.518 0-.257-.01-1.105-.014-2.005-2.921.652-3.538-1.27-3.538-1.27-.477-1.244-1.165-1.575-1.165-1.575-.953-.668.071-.655.071-.655 1.055.076 1.61 1.11 1.61 1.11.936 1.646 2.456 1.17 3.056.895.094-.696.366-1.171.666-1.44-2.332-.272-4.784-1.195-4.784-5.32 0-1.176.41-2.136 1.082-2.89-.109-.271-.468-1.366.102-2.85 0 0 .882-.288 2.888 1.105a9.833 9.833 0 0 1 2.628-.363 9.857 9.857 0 0 1 2.63.363c2.005-1.393 2.885-1.104 2.885-1.104.572 1.483.212 2.578.103 2.849.674.754 1.08 1.714 1.08 2.89 0 4.135-2.455 5.045-4.794 5.312.377.334.712.989.712 1.993 0 1.44-.011 2.6-.011 2.955 0 .286.188.622.72.516 4.17-1.425 7.175-5.459 7.175-10.214 0-5.946-4.7-10.766-10.5-10.766Z"
        clipRule="evenodd"
      />
      <path
        fill="currentColor"
        d="M4.687 15.677c-.023.053-.105.07-.18.033-.076-.036-.119-.109-.094-.162.023-.055.105-.07.18-.**************.109.094.163Zm.425.486c-.05.047-.148.025-.214-.05-.069-.075-.082-.176-.03-.224.05-.047.146-.*************.**************.224Zm.414.62c-.064.046-.17.003-.234-.093-.065-.096-.065-.21.001-.257.065-.046.17-.**************.098.064.213-.002.26Zm.568.599c-.058.065-.18.047-.27-.041-.092-.087-.117-.21-.06-.275.058-.066.182-.047.272.**************.211.058.276Zm.782.348c-.026.084-.143.122-.262.087-.12-.037-.197-.136-.173-.221.025-.085.143-.125.263-.***************.135.172.22Zm.86.064c.002.09-.098.163-.223.164-.126.003-.228-.069-.229-.156 0-.09.099-.162.224-.165.125-.**************.157Zm.799-.139c.015.086-.072.175-.196.199-.122.023-.235-.03-.25-.116-.015-.09.073-.178.195-.201.124-.*************.118Z"
      />
    </svg>
  ),
  Sidebar: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="currentColor"
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm240-80h400v-480H400v480Zm-80 0v-480H160v480h160Zm-160 0v-480 480Zm160 0h80-80Zm0-480h80-80Z" />
    </svg>
  ),
  SidebarFilled: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="currentColor"
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h160v640H160Zm240 0v-640h400q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H400Z" />
    </svg>
  ),
  Reconnect: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={props.size}
      height={props.size}
      fill="currentColor"
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M760-120q-39 0-70-22.5T647-200H440q-66 0-113-47t-47-113q0-66 47-113t113-47h80q33 0 56.5-23.5T600-600q0-33-23.5-56.5T520-680H313q-13 35-43.5 57.5T200-600q-50 0-85-35t-35-85q0-50 35-85t85-35q39 0 69.5 22.5T313-760h207q66 0 113 47t47 113q0 66-47 113t-113 47h-80q-33 0-56.5 23.5T360-360q0 33 23.5 56.5T440-280h207q13-35 43.5-57.5T760-360q50 0 85 35t35 85q0 50-35 85t-85 35ZM200-680q17 0 28.5-11.5T240-720q0-17-11.5-28.5T200-760q-17 0-28.5 11.5T160-720q0 17 11.5 28.5T200-680Z" />
    </svg>
  ),
  DotRaster: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={8}
      height={8}
      fill="none"
      {...props}
    >
      <mask
        id="a"
        width={8}
        height={8}
        x={0}
        y={0}
        maskUnits="userSpaceOnUse"
        style={{
          maskType: "alpha",
        }}
      >
        <circle cx={4} cy={4} r={4} fill="#D9D9D9" />
      </mask>
      <g fill="currentColor" mask="url(#a)">
        <path d="m4.58-1.398.717.698-6.28 6.447-.717-.698zM7.27-.072l.716.698L.45 8.363l-.716-.698zM9.962 1.255l.717.698-8.045 8.258-.717-.698z" />
      </g>
    </svg>
  ),
  Gmail: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 48 48"
      {...props}
    >
      <path
        fill="#4caf50"
        d="m45 16.2-5 2.75-5 4.75V40h7a3 3 0 0 0 3-3V16.2z"
      />
      <path
        fill="#1e88e5"
        d="m3 16.2 3.614 1.71L13 23.7V40H6a3 3 0 0 1-3-3V16.2z"
      />
      <path
        fill="#e53935"
        d="m35 11.2-11 8.25-11-8.25-1 5.8 1 6.7 11 8.25 11-8.25 1-6.7z"
      />
      <path
        fill="#c62828"
        d="M3 12.298V16.2l10 7.5V11.2L9.876 8.859A4.298 4.298 0 0 0 3 12.298z"
      />
      <path
        fill="#fbc02d"
        d="M45 12.298V16.2l-10 7.5V11.2l3.124-2.341A4.298 4.298 0 0 1 45 12.298z"
      />
    </svg>
  ),
  Outlook: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 48 48"
      {...props}
    >
      <path
        fill="#1a237e"
        d="m43.607 23.752-7.162-4.172v11.594H44v-6.738a.793.793 0 0 0-.393-.684z"
      />
      <path
        fill="#0c4999"
        d="M33.919 8.84h9.046V7.732c0-.957-.775-1.732-1.731-1.732H17.667c-.956 0-1.732.775-1.732 1.732V8.84h17.984z"
      />
      <path
        fill="#0f73d9"
        d="M33.919 33.522h7.314c.956 0 1.732-.775 1.732-1.732v-6.827h-9.046v8.559z"
      />
      <path
        fill="#0f439d"
        d="M15.936 24.964v6.827c0 .956.775 1.732 1.732 1.732h7.273v-8.558h-9.005z"
      />
      <path fill="#2ecdfd" d="M33.919 8.84h9.046v8.027h-9.046z" />
      <path fill="#1c5fb0" d="M15.936 8.84h9.005v8.027h-9.005z" />
      <path fill="#1467c7" d="M24.94 24.964h8.979v8.558H24.94z" />
      <path fill="#1690d5" d="M24.94 8.84h8.979v8.027H24.94z" />
      <path fill="#1bb4ff" d="M33.919 16.867h9.046v8.096h-9.046z" />
      <path fill="#074daf" d="M15.936 16.867h9.005v8.096h-9.005z" />
      <path fill="#2076d4" d="M24.94 16.867h8.979v8.096H24.94z" />
      <path
        fill="#2ed0ff"
        d="M15.441 42h26.87c.933 0 1.689-.756 1.689-1.689V24.438s-.03.658-1.751 1.617c-1.3.724-27.505 15.511-27.505 15.511s.234.434.697.434z"
      />
      <path
        fill="#139fe2"
        d="M42.279 41.997 15.689 42A1.689 1.689 0 0 1 14 40.311V25.067l29.363 16.562c-.245.196-.556.368-1.084.368z"
      />
      <path
        fill="#00488d"
        d="M22.319 34H5.681A1.682 1.682 0 0 1 4 32.319V15.681C4 14.753 4.753 14 5.681 14h16.638c.928 0 1.681.753 1.681 1.681v16.638c0 .928-.753 1.681-1.681 1.681z"
      />
      <path
        fill="#fff"
        d="M13.914 18.734c-3.131 0-5.017 2.392-5.017 5.343s1.879 5.342 5.017 5.342c3.139 0 5.017-2.392 5.017-5.342 0-2.951-1.886-5.343-5.017-5.343zm0 8.882c-1.776 0-2.838-1.584-2.838-3.539s1.067-3.539 2.838-3.539c1.771 0 2.839 1.585 2.839 3.539s-1.064 3.539-2.839 3.539z"
      />
    </svg>
  ),
  CreateTransaction: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={props.size ?? 24}
      height={props.size ?? 24}
      fill="currentColor"
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M640-121v-120H520v-80h120v-120h80v120h120v80H720v120h-80ZM160-240v-80h283q-3 21-2.5 40t3.5 40H160Zm0-160v-80h386q-23 16-41.5 36T472-400H160Zm0-160v-80h600v80H160Zm0-160v-80h600v80H160Z" />
    </svg>
  ),
  ArrowCoolDown: (props: any) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="currentColor"
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M480-80 200-360l56-57 184 184v-287h80v287l184-183 56 56L480-80Zm-40-520v-120h80v120h-80Zm0-200v-80h80v80h-80Z" />
    </svg>
  ),
  Delete: ({
    size = 24,
    ...props
  }: { size?: number } & React.SVGProps<SVGSVGElement>) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      fill="currentColor"
      viewBox="0 -960 960 960"
      {...props}
    >
      <path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z" />
    </svg>
  ),
  Tracker: (props: SVGIconProps) => (
    <SVGIcon {...props}>
      <path d="M7.65956 2.29166V1.04166H12.3391V2.29166H7.65956ZM9.37435 11.5064H10.6243V6.82686H9.37435V11.5064ZM9.99935 17.9167C9.02504 17.9167 8.10838 17.7308 7.24935 17.3589C6.39032 16.9871 5.63928 16.4796 4.99622 15.8364C4.35303 15.1934 3.84553 14.4424 3.47372 13.5833C3.10192 12.7243 2.91602 11.8076 2.91602 10.8333C2.91602 9.85902 3.10192 8.94235 3.47372 8.08332C3.84553 7.2243 4.35303 6.47325 4.99622 5.8302C5.63928 5.187 6.39032 4.6795 7.24935 4.3077C8.10838 3.93589 9.02504 3.74999 9.99935 3.74999C10.8338 3.74999 11.6375 3.89159 12.4104 4.17478C13.1833 4.45784 13.901 4.86325 14.5635 5.39103L15.6018 4.3527L16.48 5.23082L15.4416 6.26916C15.9694 6.93166 16.3748 7.64936 16.6579 8.42228C16.9411 9.1952 17.0827 9.99888 17.0827 10.8333C17.0827 11.8076 16.8968 12.7243 16.525 13.5833C16.1532 14.4424 15.6457 15.1934 15.0025 15.8364C14.3594 16.4796 13.6084 16.9871 12.7493 17.3589C11.8903 17.7308 10.9737 17.9167 9.99935 17.9167ZM9.99935 16.6667C11.6105 16.6667 12.9855 16.0972 14.1243 14.9583C15.2632 13.8194 15.8327 12.4444 15.8327 10.8333C15.8327 9.22221 15.2632 7.84721 14.1243 6.70832C12.9855 5.56943 11.6105 4.99999 9.99935 4.99999C8.38824 4.99999 7.01324 5.56943 5.87435 6.70832C4.73546 7.84721 4.16602 9.22221 4.16602 10.8333C4.16602 12.4444 4.73546 13.8194 5.87435 14.9583C7.01324 16.0972 8.38824 16.6667 9.99935 16.6667Z" />
    </SVGIcon>
  ),
  Inbox: ArchiveIcon,
  Close: MdClose,
  X: FaXTwitter,
  Discord: PiDiscordLogo,
  PdfOutline: MdOutlinePictureAsPdf,
  Amount: MdOutlineEqualizer,
  Attachments: MdOutlineAttachFile,
  GithubOutline: FiGithub,
  Refresh: MdRefresh,
  Currency: MdAttachMoney,
  Inventory2: MdInventory2,
  Notifications: MdOutlineNotificationsNone,
  ChevronDown: MdExpandMore,
  ChevronUp: MdExpandLess,
  TrendingUp: MdTrendingUp,
  TrendingDown: MdTrendingDown,
  Category: MdOutlineCategory,
  Visibility: MdOutlineVisibility,
  Face: MdOutlineFace,
  MoreHoriz: MdMoreHoriz,
  Pdf: MdPictureAsPdf,
  BrokenImage: MdOutlineBrokenImage,
  Description: MdDescription,
  FolderZip: MdFolderZip,
  ChevronRight: MdChevronRight,
  ChevronLeft: MdChevronLeft,
  ArrowBack: MdArrowBack,
  Folder: MdFolder,
  Search: MdSearch,
  Error: MdErrorOutline,
  AI: MdAutoAwesome,
  AIOutline: MdOutlineAutoAwesome,
  Time: MdOutlineMoreTime,
  Add: MdOutlineAdd,
  Copy: MdOutlineContentCopy,
  Share: MdIosShare,
  Play: MdPlayArrow,
  MoreVertical: MdOutlineMoreVert,
  Match: MdOutlineTask,
  Email: MdOutlineEmail,
  Tune: MdOutlineTune,
  Change: MdChangeHistory,
  CalendarMonth: MdOutlineCalendarMonth,
  Sort: MdSort,
  Palette: MdOutlinePalette,
  Menu: MdMenu,
  Mute: MdOutlineVolumeOff,
  UnMute: MdOutlineVolumeUp,
  Clear: MdOutlineClear,
  Filter: MdOutlineFilterList,
  Status: MdOutlineStyle,
  Accounts: MdOutlineAccountBalance,
  ArrowRightAlt: MdArrowRightAlt,
  AccountCircle: MdOutlineAccountCircle,
  Repeat: MdOutlineRepeat,
  ProjectStatus: MdDownloading,
  Edit: MdOutlineEditNote,
  OpenInNew: MdOutlineOpenInNew,
  DragIndicator: MdDragIndicator,
  ExternalLink: MdOutlineLaunch,
  CropFree: MdOutlineCropFree,
  DateFormat: MdOutlineFactCheck,
  Tax: MdOutlineAssuredWorkload,
  Vat: MdOutlineCalculate,
  CurrencyOutline: MdOutlineAttachMoney,
  SnippetFolder: MdSnippetFolder,
  ConfirmationNumber: MdOutlineConfirmationNumber,
  QrCode: MdOutlineQrCode2,
  ArrowOutward: MdOutlineArrowOutward,
  Straighten: MdOutlineSquareFoot,
  Files: MdOutlineInventory2,
  GridView: MdOutlineGridView,
  ListView: MdOutlineReorder,
  ArrowDownward: MdArrowDownward,
  ArrowUpward: MdArrowUpward,
  SubdirectoryArrowLeft: MdOutlineSubdirectoryArrowLeft,
};
