{"name": "@midday/ui", "version": "1.0.0", "private": true, "sideEffects": false, "files": ["tailwind.config.ts", "postcss.config.js", "globals.css"], "scripts": {"clean": "rm -rf .turbo node_modules", "lint": "biome check .", "format": "biome format --write .", "typecheck": "tsc --noEmit"}, "exports": {"./animated-size-container": "./src/components/animated-size-container.tsx", "./accordion": "./src/components/accordion.tsx", "./alert-dialog": "./src/components/alert-dialog.tsx", "./alert": "./src/components/alert.tsx", "./chart": "./src/components/chart.tsx", "./currency-input": "./src/components/currency-input.tsx", "./submit-button": "./src/components/submit-button.tsx", "./avatar": "./src/components/avatar.tsx", "./button": "./src/components/button.tsx", "./calendar": "./src/components/calendar.tsx", "./card": "./src/components/card.tsx", "./slider": "./src/components/slider.tsx", "./carousel": "./src/components/carousel.tsx", "./checkbox": "./src/components/checkbox.tsx", "./collapsible": "./src/components/collapsible.tsx", "./combobox": "./src/components/combobox.tsx", "./combobox-dropdown": "./src/components/combobox-dropdown.tsx", "./command": "./src/components/command.tsx", "./context-menu": "./src/components/context-menu.tsx", "./date-range-picker": "./src/components/date-range-picker.tsx", "./dialog": "./src/components/dialog.tsx", "./drawer": "./src/components/drawer.tsx", "./dropdown-menu": "./src/components/dropdown-menu.tsx", "./form": "./src/components/form.tsx", "./editor": "./src/components/editor/index.tsx", "./globals.css": "./src/globals.css", "./hover-card": "./src/components/hover-card.tsx", "./icons": "./src/components/icons.tsx", "./input": "./src/components/input.tsx", "./input-otp": "./src/components/input-otp.tsx", "./label": "./src/components/label.tsx", "./navigation-menu": "./src/components/navigation-menu.tsx", "./popover": "./src/components/popover.tsx", "./postcss": "./postcss.config.js", "./progress": "./src/components/progress.tsx", "./radio-group": "./src/components/radio-group.tsx", "./scroll-area": "./src/components/scroll-area.tsx", "./select": "./src/components/select.tsx", "./sheet": "./src/components/sheet.tsx", "./badge": "./src/components/badge.tsx", "./separator": "./src/components/separator.tsx", "./skeleton": "./src/components/skeleton.tsx", "./spinner": "./src/components/spinner.tsx", "./switch": "./src/components/switch.tsx", "./multiple-selector": "./src/components/multiple-selector.tsx", "./table": "./src/components/table.tsx", "./tabs": "./src/components/tabs.tsx", "./tailwind.config": "./tailwind.config.ts", "./textarea": "./src/components/textarea.tsx", "./toast": "./src/components/toast.tsx", "./toaster": "./src/components/toaster.tsx", "./tooltip": "./src/components/tooltip.tsx", "./time-range-input": "./src/components/time-range-input.tsx", "./use-toast": "./src/components/use-toast.tsx", "./cn": "./src/utils/cn.ts", "./truncate": "./src/utils/truncate.ts", "./hooks": "./src/hooks/index.ts", "./quantity-input": "./src/components/quantity-input.tsx"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-bold": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jsonfile": "^6.1.0", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "react-day-picker": "8.10.1", "react-icons": "^5.5.0", "react-number-format": "^5.4.4", "recharts": "^2.15.3", "tailwind-merge": "2.5.3", "tailwindcss": "^3.4.13", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"autoprefixer": "^10.4.21", "react": "19.1.0", "react-dom": "19.1.0", "typescript": "^5.8.3"}}