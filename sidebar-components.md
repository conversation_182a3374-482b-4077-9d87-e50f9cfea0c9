# Sidebar Components - Complete Code

This document contains all the code needed to recreate the Midday-style sidebar with custom navigation items.

## File Structure
```
components/
├── sidebar.tsx
├── main-menu.tsx
├── team-dropdown.tsx
└── ui/
    ├── icons.tsx
    ├── avatar.tsx
    ├── button.tsx
    ├── tooltip.tsx
    └── cn.ts
```

## 1. Main Sidebar Component (`sidebar.tsx`)

```tsx
import { Icons } from "@/components/ui/icons";
import Link from "next/link";
import { MainMenu } from "./main-menu";
import { TeamDropdown } from "./team-dropdown";

export function Sidebar() {
  return (
    <aside className="h-screen flex-shrink-0 flex-col justify-between fixed top-0 pb-4 items-center hidden md:flex border-r border-border w-[70px]">
      <div className="flex flex-col items-center justify-center w-full">
        <div className="size-[70px] flex items-center justify-center border-b border-border">
          <Link href="/">
            <Icons.LogoSmall />
          </Link>
        </div>

        <MainMenu />
      </div>

      <TeamDropdown />
    </aside>
  );
}
```

## 2. Main Menu Component (`main-menu.tsx`)

```tsx
"use client";

import { cn } from "@/components/ui/cn";
import { Icons } from "@/components/ui/icons";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Link from "next/link";
import { usePathname } from "next/navigation";

const icons = {
  "/": () => <Icons.Home size={20} />,
  "/metrics": () => <Icons.BarChart size={20} />,
  "/projects": () => <Icons.Briefcase size={20} />,
  "/clients": () => <Icons.Users size={20} />,
  "/calendar": () => <Icons.Calendar size={20} />,
  "/messages": () => <Icons.MessageSquare size={20} />,
} as const;

const items = [
  {
    path: "/",
    name: "Overview",
  },
  {
    path: "/metrics",
    name: "Metrics",
  },
  {
    path: "/projects",
    name: "Jobs or Projects",
  },
  {
    path: "/clients",
    name: "Leads/Clients",
  },
  {
    path: "/calendar",
    name: "Calendar",
  },
  {
    path: "/messages",
    name: "Messages",
  },
];

interface ItemProps {
  item: { path: string; name: string };
  isActive: boolean;
  onSelect?: () => void;
}

const Item = ({ item, isActive, onSelect }: ItemProps) => {
  const Icon = icons[item.path as keyof typeof icons];

  return (
    <TooltipProvider delayDuration={70}>
      <Link prefetch href={item.path} onClick={() => onSelect?.()}>
        <Tooltip>
          <TooltipTrigger className="w-full">
            <div
              className={cn(
                "relative border border-transparent size-[40px] flex items-center md:justify-center",
                "hover:bg-accent hover:border-[#DCDAD2] hover:dark:border-[#2C2C2C] dark:text-[#666666] text-black hover:!text-primary",
                isActive &&
                  "bg-[#F2F1EF] dark:bg-secondary border-[#DCDAD2] dark:border-[#2C2C2C] dark:!text-white",
              )}
            >
              <div className="relative">
                <div className="flex space-x-3 p-0 items-center pl-2 md:pl-0">
                  <Icon />
                  <span className="flex md:hidden">{item.name}</span>
                </div>
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent
            side="left"
            className="px-3 py-1.5 text-xs hidden md:flex items-center gap-1"
            sideOffset={6}
          >
            {item.name}
          </TooltipContent>
        </Tooltip>
      </Link>
    </TooltipProvider>
  );
};

type Props = {
  onSelect?: () => void;
};

export function MainMenu({ onSelect }: Props) {
  const pathname = usePathname();
  const part = pathname?.split("/")[1];

  return (
    <div className="mt-6">
      <nav>
        <div className="flex flex-col gap-2">
          {items.map((item) => {
            const isActive =
              (pathname === "/" && item.path === "/") ||
              (pathname !== "/" && item.path.startsWith(`/${part}`));

            return (
              <Item
                key={item.path}
                item={item}
                isActive={isActive}
                onSelect={onSelect}
              />
            );
          })}
        </div>
      </nav>
    </div>
  );
}
```

## 3. Team Dropdown Component (`team-dropdown.tsx`)

```tsx
"use client";

import { Avatar, AvatarFallback, AvatarImageNext } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Icons } from "@/components/ui/icons";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { useOnClickOutside } from "usehooks-ts";

// Mock data - replace with your actual data fetching
const mockTeams = [
  {
    id: "1",
    name: "Main Team",
    logoUrl: null,
  },
  {
    id: "2", 
    name: "Project Alpha",
    logoUrl: null,
  },
];

export function TeamDropdown() {
  const [isActive, setActive] = useState(false);
  const [selectedId, setSelectedId] = useState("1");
  const [isChangingTeam, setIsChangingTeam] = useState(false);
  const ref = useRef(null);

  const teams = mockTeams;
  const sortedTeams = teams?.sort((a, b) => {
    if (a.id === selectedId) return -1;
    if (b.id === selectedId) return 1;
    return a.id.localeCompare(b.id);
  }) ?? [];

  useOnClickOutside(ref, () => {
    if (!isChangingTeam) {
      setActive(false);
    }
  });

  const toggleActive = () => setActive((prev) => !prev);

  const handleTeamChange = (teamId: string) => {
    if (teamId === selectedId) {
      toggleActive();
      return;
    }

    setIsChangingTeam(true);
    setSelectedId(teamId);
    setActive(false);
    
    // Add your team change logic here
    setTimeout(() => setIsChangingTeam(false), 500);
  };

  return (
    <motion.div ref={ref} layout className="w-[32px] h-[32px] relative">
      <AnimatePresence>
        {isActive && (
          <motion.div
            className="w-[32px] h-[32px] left-0 overflow-hidden absolute"
            style={{ zIndex: 1 }}
            initial={{ y: 0, opacity: 0 }}
            animate={{ y: -(32 + 10) * sortedTeams.length, opacity: 1 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 1.2,
            }}
          >
            <Link href="/teams/create" onClick={() => setActive(false)}>
              <Button
                className="w-[32px] h-[32px]"
                size="icon"
                variant="outline"
              >
                <Icons.Plus />
              </Button>
            </Link>
          </motion.div>
        )}
        {sortedTeams.map((team, index) => (
          <motion.div
            key={team.id}
            className="w-[32px] h-[32px] left-0 overflow-hidden absolute"
            style={{ zIndex: -index }}
            initial={{
              scale: `${100 - index * 16}%`,
              y: index * 5,
            }}
            animate={
              isActive
                ? {
                    y: -(32 + 10) * index,
                    scale: "100%",
                  }
                : {
                    scale: `${100 - index * 16}%`,
                    y: index * 5,
                  }
            }
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              mass: 1.2,
            }}
          >
            <Avatar
              className="w-[32px] h-[32px] rounded-none border border-[#DCDAD2] dark:border-[#2C2C2C] cursor-pointer"
              onClick={() => {
                if (index === 0) {
                  toggleActive();
                } else {
                  handleTeamChange(team?.id ?? "");
                }
              }}
            >
              <AvatarImageNext
                src={team?.logoUrl ?? ""}
                alt={team?.name ?? ""}
                width={20}
                height={20}
                quality={100}
              />
              <AvatarFallback className="rounded-none w-[32px] h-[32px]">
                <span className="text-xs">
                  {team?.name?.charAt(0)?.toUpperCase()}
                  {team?.name?.charAt(1)?.toUpperCase()}
                </span>
              </AvatarFallback>
            </Avatar>
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
}
```

## 4. Icons Component (`ui/icons.tsx`)

```tsx
import { LucideProps } from "lucide-react";

// Example icons - replace with your preferred icon library
export const Icons = {
  LogoSmall: (props: LucideProps) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
      <path d="M8 12h8" stroke="currentColor" strokeWidth="2"/>
      <path d="M12 8v8" stroke="currentColor" strokeWidth="2"/>
    </svg>
  ),
  Home: (props: LucideProps) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
      <polyline points="9,22 9,12 15,12 15,22"/>
    </svg>
  ),
  BarChart: (props: LucideProps) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <line x1="12" y1="20" x2="12" y2="10"/>
      <line x1="18" y1="20" x2="18" y2="4"/>
      <line x1="6" y1="20" x2="6" y2="16"/>
    </svg>
  ),
  Briefcase: (props: LucideProps) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <rect x="2" y="7" width="20" height="14" rx="2" ry="2"/>
      <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
    </svg>
  ),
  Users: (props: LucideProps) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
      <circle cx="9" cy="7" r="4"/>
      <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
      <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
    </svg>
  ),
  Calendar: (props: LucideProps) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
      <line x1="16" y1="2" x2="16" y2="6"/>
      <line x1="8" y1="2" x2="8" y2="6"/>
      <line x1="3" y1="10" x2="21" y2="10"/>
    </svg>
  ),
  MessageSquare: (props: LucideProps) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
    </svg>
  ),
  Plus: (props: LucideProps) => (
    <svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <line x1="12" y1="5" x2="12" y2="19"/>
      <line x1="5" y1="12" x2="19" y2="12"/>
    </svg>
  ),
};
```

## 5. Utility Functions (`ui/cn.ts`)

```tsx
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
```

## 6. CSS Variables (Add to your global CSS)

```css
:root {
  /* Sidebar Dimensions */
  --sidebar-width: 70px;
  --sidebar-item-size: 40px;
  --sidebar-avatar-size: 32px;
  --sidebar-icon-size: 20px;

  /* Colors - Light Mode */
  --sidebar-bg: #ffffff;
  --sidebar-border: hsl(45, 5%, 85%);
  --sidebar-item-bg-active: #f2f1ef;
  --sidebar-item-border-active: #dcdad2;
  --sidebar-item-hover-bg: hsl(40, 10%, 94%);

  /* Colors - Dark Mode */
  --sidebar-bg-dark: hsl(0, 0%, 7%);
  --sidebar-border-dark: hsl(0, 0%, 17%);
  --sidebar-item-bg-active-dark: hsl(0, 0%, 11%);
  --sidebar-item-border-active-dark: #2c2c2c;

  /* Text Colors */
  --sidebar-text: #000000;
  --sidebar-text-dark: #666666;
  --sidebar-text-active-dark: #ffffff;
  --sidebar-icon-color: #000000;
  --sidebar-icon-color-dark: #666666;

  /* Theme Variables */
  --background: 0, 0%, 100%;
  --foreground: 0, 0%, 7%;
  --border: 45, 5%, 85%;
  --primary: 240 5.9% 10%;
  --secondary: 40, 11%, 89%;
  --accent: 40, 10%, 94%;
}

.dark {
  --background: 0, 0%, 7%;
  --foreground: 0, 0%, 98%;
  --border: 0, 0%, 17%;
  --primary: 0 0% 98%;
  --secondary: 0, 0%, 11%;
  --accent: 0, 0%, 9%;
}
```

## 7. Required Dependencies

```json
{
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "framer-motion": "^10.0.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "usehooks-ts": "^2.0.0",
    "lucide-react": "^0.300.0"
  }
}
```

## 8. Tailwind CSS Configuration

Make sure your `tailwind.config.js` includes these classes:

```js
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: "hsl(var(--primary))",
        secondary: "hsl(var(--secondary))",
        accent: "hsl(var(--accent))",
      },
    },
  },
  plugins: [],
}
```

## 9. Basic UI Components

You'll also need these basic UI components. Here are minimal implementations:

### Avatar Component (`ui/avatar.tsx`)
```tsx
import * as React from "react";
import { cn } from "./cn";

const Avatar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",
      className
    )}
    {...props}
  />
));

const AvatarImage = React.forwardRef<
  HTMLImageElement,
  React.ImgHTMLAttributes<HTMLImageElement>
>(({ className, ...props }, ref) => (
  <img
    ref={ref}
    className={cn("aspect-square h-full w-full", className)}
    {...props}
  />
));

const AvatarFallback = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-full bg-muted",
      className
    )}
    {...props}
  />
));

// For Next.js Image component compatibility
const AvatarImageNext = AvatarImage;

export { Avatar, AvatarImage, AvatarImageNext, AvatarFallback };
```

### Button Component (`ui/button.tsx`)

```tsx
import * as React from "react";
import { cn } from "./cn";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", ...props }, ref) => {
    return (
      <button
        className={cn(
          "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",
          {
            "bg-primary text-primary-foreground hover:bg-primary/90": variant === "default",
            "border border-input bg-background hover:bg-accent hover:text-accent-foreground": variant === "outline",
            "hover:bg-accent hover:text-accent-foreground": variant === "ghost",
          },
          {
            "h-10 px-4 py-2": size === "default",
            "h-9 rounded-md px-3": size === "sm",
            "h-11 rounded-md px-8": size === "lg",
            "h-10 w-10": size === "icon",
          },
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

export { Button };
```

### Tooltip Component (`ui/tooltip.tsx`)

```tsx
import * as React from "react";
import { cn } from "./cn";

interface TooltipProps {
  children: React.ReactNode;
}

interface TooltipContentProps extends React.HTMLAttributes<HTMLDivElement> {
  side?: "top" | "right" | "bottom" | "left";
  sideOffset?: number;
}

const TooltipProvider = ({ children }: TooltipProps) => {
  return <>{children}</>;
};

const Tooltip = ({ children }: TooltipProps) => {
  return <div className="relative group">{children}</div>;
};

const TooltipTrigger = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
  <div ref={ref} className={className} {...props}>
    {children}
  </div>
));

const TooltipContent = React.forwardRef<HTMLDivElement, TooltipContentProps>(
  ({ className, side = "top", sideOffset = 4, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "absolute z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95",
        "invisible group-hover:visible",
        {
          "bottom-full left-1/2 -translate-x-1/2": side === "top",
          "top-1/2 left-full -translate-y-1/2": side === "right",
          "top-full left-1/2 -translate-x-1/2": side === "bottom",
          "top-1/2 right-full -translate-y-1/2": side === "left",
        },
        className
      )}
      style={{
        marginBottom: side === "top" ? sideOffset : undefined,
        marginLeft: side === "right" ? sideOffset : undefined,
        marginTop: side === "bottom" ? sideOffset : undefined,
        marginRight: side === "left" ? sideOffset : undefined,
      }}
      {...props}
    >
      {children}
    </div>
  )
);

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };
```

## 10. Usage

1. **Install dependencies:**
```bash
npm install next react framer-motion clsx tailwind-merge usehooks-ts lucide-react
```

2. **Add the CSS variables to your global stylesheet**

3. **Copy all components into your project structure**

4. **Use the sidebar in your layout:**
```tsx
import { Sidebar } from "@/components/sidebar";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <main className="flex-1 ml-0 md:ml-[70px]">
        {children}
      </main>
    </div>
  );
}
```

## Key Features

- **70px fixed width** sidebar
- **Responsive design** (hidden on mobile, visible on desktop)
- **Tooltip navigation** with hover effects
- **Active state management** based on current route
- **Animated team dropdown** with stacked avatars
- **Dark mode support** with CSS variables
- **Customizable navigation items** - easily modify the `items` array in `main-menu.tsx`

The sidebar will automatically highlight the active page based on the current URL path and provide smooth hover animations and tooltips for better user experience.
