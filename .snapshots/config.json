{"excluded_patterns": [".git", ".giti<PERSON>re", "gradle", "gradlew", "gradlew.*", "node_modules", ".snapshots", ".idea", ".vscode", "*.log", "*.tmp", "target", "dist", "build", ".DS_Store", "*.bak", "*.swp", "*.swo", "*.lock", "*.iml", "coverage", "*.min.js", "*.min.css", "__pycache__", ".marketing", ".env", ".env.*", "*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp", "*.tiff", "*.ico", "*.svg", "*.webp", "*.psd", "*.ai", "*.eps", "*.indd", "*.raw", "*.cr2", "*.nef", "*.mp4", "*.mov", "*.avi", "*.wmv", "*.flv", "*.mkv", "*.webm", "*.m4v", "*.wfp", "*.prp<PERSON>j", "*.aep", "*.psb", "*.xcf", "*.sketch", "*.fig", "*.xd", "*.db", "*.sqlite", "*.sqlite3", "*.mdb", "*.accdb", "*.frm", "*.myd", "*.myi", "*.ibd", "*.dbf", "*.rdb", "*.aof", "*.pdb", "*.sdb", "*.s3db", "*.ddb", "*.db-shm", "*.db-wal", "*.sqlitedb", "*.sql.gz", "*.bak.sql", "dump.sql", "dump.rdb", "*.vsix", "*.jar", "*.war", "*.ear", "*.zip", "*.tar", "*.tar.gz", "*.tgz", "*.rar", "*.7z", "*.exe", "*.dll", "*.so", "*.dyl<PERSON>", "*.app", "*.dmg", "*.iso", "*.msi", "*.deb", "*.rpm", "*.apk", "*.aab", "*.ipa", "*.pkg", "*.nupkg", "*.snap", "*.whl", "*.gem", "*.pyc", "*.pyo", "*.pyd", "*.class", "*.o", "*.obj", "*.lib", "*.a", "*.map", ".npmrc"], "default": {"default_prompt": "Enter your prompt here", "default_include_all_files": false, "default_include_entire_project_structure": true}, "included_patterns": ["build.gradle", "settings.gradle", "gradle.properties", "pom.xml", "<PERSON><PERSON><PERSON>", "CMakeLists.txt", "package.json", "requirements.txt", "Pipfile", "Gem<PERSON>le", "composer.json", ".editorconfig", ".eslintrc.json", ".eslintrc.js", ".prettier<PERSON>", ".babelrc", ".dockerignore", ".gitattributes", ".stylelintrc", ".npmrc"]}