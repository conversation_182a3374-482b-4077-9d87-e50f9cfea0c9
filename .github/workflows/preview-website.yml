name: Preview Deployment - Website
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEBSITE }}
  TURBO_TOKEN: ${{ secrets.VERCEL_TOKEN }}
  TURBO_TEAM: ${{ secrets.VERCEL_ORG_ID }}
on:
  push:
    branches-ignore:
      - main
    paths:
      - apps/website/**
      - packages/**
jobs:
  deploy-preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Install dependencies
        run: bun install
      # - name: 🔦 Run linter
      #   run: bun run lint
      # - name: 🪐 Check TypeScript
      #   run: bun run typecheck
      # working-directory: ./apps/website
      - name: 📤 Pull Vercel Environment Information
        run: bunx vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
      - name: 🏗 Build Project Artifacts
        run: bunx vercel build --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: bunx vercel deploy --prebuilt --archive=tgz --token=${{ secrets.VERCEL_TOKEN }}